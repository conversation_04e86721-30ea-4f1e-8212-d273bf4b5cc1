// Get DOM elements
const video = document.getElementById('video');
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const loading = document.getElementById('loading');

// UI elements
const startScreen = document.getElementById('startScreen');
const pauseScreen = document.getElementById('pauseScreen');
const gameUI = document.getElementById('gameUI');
const startBtn = document.getElementById('startBtn');
const pauseBtn = document.getElementById('pauseBtn');
const resumeBtn = document.getElementById('resumeBtn');
const restartBtn = document.getElementById('restartBtn');

// Score display elements
const scoreElement = document.getElementById('score');
const starsInHandElement = document.getElementById('starsInHand');
const caughtStarsElement = document.getElementById('caughtStars');
const comboElement = document.getElementById('combo');
const particleContainer = document.getElementById('particleContainer');

const canvasWidth = 640;
const canvasHeight = 480;

// Game state
let gameState = 'waiting'; // 'waiting', 'playing', 'paused'
let score = 0;
let starsInHand = [];
let combo = 1;
let lastScoreTime = 0;
const SCORE_INTERVAL = 2000; // Score added every 2 seconds
const STAR_LIFETIME = 8000; // Star lifetime in hand: 8 seconds

// Setup Matter.js physics engine
const { Engine, Runner, World, Bodies, Body, Events, Composite } = Matter;
const engine = Engine.create();
engine.world.gravity.y = 0.2; // Softer gravity

// Create boundaries (walls)
const leftWall = Bodies.rectangle(-25, canvasHeight / 2, 50, canvasHeight, { isStatic: true });
const rightWall = Bodies.rectangle(canvasWidth + 25, canvasHeight / 2, 50, canvasHeight, { isStatic: true });
World.add(engine.world, [leftWall, rightWall]);

// Hand slingshot segments - will be created dynamically
let slingshotBodies = [];
let slingshotConstraints = [];

// New variables for catching and throwing
let caughtStars = []; // 改为数组，支持抓住多个星星
let lastHandPosition = null;
let handVelocity = { x: 0, y: 0 };
let handClosed = false;
let maxCaughtStars = 8; // 最多同时抓住8个星星

// Game timers
let starGenerationInterval;
let scoreUpdateInterval;
let runner;

// Star management
class StarManager {
    constructor() {
        this.stars = new Map(); // Store stars and their creation time
    }

    createStar() {
        if (gameState !== 'playing') return;
        
        const allBodies = Composite.allBodies(engine.world);
        const starCount = allBodies.filter(b => b.label === 'star').length;

        if (starCount < 15) { // Reduce the number of stars at the same time
            const x = Math.random() * (canvasWidth - 40) + 20;
            const star = Bodies.polygon(x, -50, 5, 20, {
                restitution: 0.8,
                friction: 0.01,
                label: 'star',
                angle: Math.random() * Math.PI * 2,
                angularVelocity: (Math.random() - 0.5) * 0.1,
            });
            
            // Add star identifier
            star.starId = Date.now() + Math.random();
            star.createdTime = Date.now();
            
            World.add(engine.world, star);
            this.stars.set(star.starId, {
                body: star,
                createdTime: Date.now(),
                caughtTime: null,
                isInHand: false
            });
        }
    }

    catchStar(star) {
        if (this.stars.has(star.starId)) {
            const starData = this.stars.get(star.starId);
            starData.caughtTime = Date.now();
            starData.isInHand = true;
            
            // Add to hand stars list
            if (!starsInHand.find(s => s.starId === star.starId)) {
                starsInHand.push(starData);
                updateUI();
                
                // Create catch effect
                createCatchEffect(star.position.x, star.position.y);
                
                // Increase combo
                combo = Math.min(combo + 0.2, 5); // Maximum 5x combo
            }
        }
    }

    updateStarsLifetime() {
        const currentTime = Date.now();
        const starsToRemove = [];
        
        starsInHand.forEach((starData, index) => {
            if (currentTime - starData.caughtTime > STAR_LIFETIME) {
                starsToRemove.push(index);
                
                // Create disappear effect
                this.createDisappearEffect(starData.body);
                
                // Remove from physical world
                World.remove(engine.world, starData.body);
                this.stars.delete(starData.body.starId);
            }
        });
        
        // Remove in reverse order to avoid index issues
        starsToRemove.reverse().forEach(index => {
            starsInHand.splice(index, 1);
        });
        
        updateUI();
    }

    createDisappearEffect(star) {
        // Star disappear explosion effect
        const explosion = document.createElement('div');
        explosion.className = 'star-explosion';
        explosion.style.left = (star.position.x - 30) + 'px';
        explosion.style.top = (star.position.y - 30) + 'px';
        document.body.appendChild(explosion);
        
        // Create particle effect
        for (let i = 0; i < 12; i++) {
            setTimeout(() => {
                createParticle(star.position.x, star.position.y);
            }, i * 50);
        }
        
        // Remove explosion effect element
        setTimeout(() => {
            if (explosion.parentNode) {
                explosion.parentNode.removeChild(explosion);
            }
        }, 600);
    }

    removeFallenStars() {
        const allBodies = Composite.allBodies(engine.world);
        allBodies.forEach(body => {
            if (body.label === 'star' && body.position.y > canvasHeight + 50) {
                World.remove(engine.world, body);
                if (this.stars.has(body.starId)) {
                    this.stars.delete(body.starId);
                }
                
                // If a star in hand falls off, remove from list
                const handIndex = starsInHand.findIndex(s => s.body.starId === body.starId);
                if (handIndex !== -1) {
                    starsInHand.splice(handIndex, 1);
                    // Decrease combo
                    combo = Math.max(combo - 0.5, 1);
                    updateUI();
                }
            }
        });
    }
}

const starManager = new StarManager();

// Particle system
function createParticle(x, y) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.left = x + 'px';
    particle.style.top = y + 'px';
    
    // Random direction
    const angle = Math.random() * Math.PI * 2;
    const velocity = 20 + Math.random() * 30;
    particle.style.setProperty('--vx', Math.cos(angle) * velocity + 'px');
    particle.style.setProperty('--vy', Math.sin(angle) * velocity + 'px');
    
    particleContainer.appendChild(particle);
    
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 3000);
}

function createCatchEffect(x, y) {
    // Create flash effect
    for (let i = 0; i < 8; i++) {
        setTimeout(() => {
            createParticle(x + (Math.random() - 0.5) * 40, y + (Math.random() - 0.5) * 40);
        }, i * 100);
    }
}

function showScorePopup(points, x, y) {
    const popup = document.createElement('div');
    popup.className = 'score-popup';
    popup.textContent = `+${points}`;
    popup.style.left = x + 'px';
    popup.style.top = y + 'px';
    document.body.appendChild(popup);
    
    setTimeout(() => {
        if (popup.parentNode) {
            popup.parentNode.removeChild(popup);
        }
    }, 1500);
}

// Score system
function updateScore() {
    if (gameState !== 'playing') return;
    
    const currentTime = Date.now();
    if (currentTime - lastScoreTime >= SCORE_INTERVAL) {
        const starCount = starsInHand.length;
        if (starCount > 0) {
            // Score calculation: base score * square of star count * combo multiplier
            const baseScore = 10;
            const starBonus = starCount * starCount;
            const comboBonus = Math.floor(combo);
            const points = baseScore * starBonus * comboBonus;
            
            score += points;
            
            // Show score popup
            if (lastHandPosition) {
                showScorePopup(points, lastHandPosition.x, lastHandPosition.y - 50);
            }
            
            // Increase combo
            combo = Math.min(combo + 0.1, 5);
            
            updateUI();
        } else {
            // No stars, decrease combo
            combo = Math.max(combo - 0.1, 1);
            updateUI();
        }
        
        lastScoreTime = currentTime;
    }
}

// Update UI display
function updateUI() {
    scoreElement.textContent = score.toLocaleString();
    starsInHandElement.textContent = starsInHand.length;
    caughtStarsElement.textContent = caughtStars.length;
    comboElement.textContent = `x${combo.toFixed(1)}`;
}

// Game state management
function startGame() {
    gameState = 'playing';
    score = 0;
    starsInHand = [];
    combo = 1;
    lastScoreTime = Date.now();
    
    // Hide start screen, show game UI
    startScreen.style.display = 'none';
    gameUI.style.display = 'block';
    pauseBtn.style.display = 'block';
    
    updateUI();
    
    // Start star generation
    starGenerationInterval = setInterval(() => {
        starManager.createStar();
    }, 800);
    
    // Start score update
    scoreUpdateInterval = setInterval(() => {
        updateScore();
        starManager.updateStarsLifetime();
    }, 100);
}

function pauseGame() {
    gameState = 'paused';
    pauseScreen.style.display = 'flex';
    
    if (starGenerationInterval) {
        clearInterval(starGenerationInterval);
    }
    if (scoreUpdateInterval) {
        clearInterval(scoreUpdateInterval);
    }
}

function resumeGame() {
    gameState = 'playing';
    pauseScreen.style.display = 'none';
    lastScoreTime = Date.now(); // Reset score timer
    
    // Restart timers
    starGenerationInterval = setInterval(() => {
        starManager.createStar();
    }, 800);
    
    scoreUpdateInterval = setInterval(() => {
        updateScore();
        starManager.updateStarsLifetime();
    }, 100);
}

function restartGame() {
    // Clear existing state
    gameState = 'waiting';
    
    // Clear all stars
    const allBodies = Composite.allBodies(engine.world);
    allBodies.forEach(body => {
        if (body.label === 'star') {
            World.remove(engine.world, body);
        }
    });
    
    // Clear timers
    if (starGenerationInterval) {
        clearInterval(starGenerationInterval);
    }
    if (scoreUpdateInterval) {
        clearInterval(scoreUpdateInterval);
    }
    
    // Reset variables
    starManager.stars.clear();
    starsInHand = [];
    caughtStars = [];
    
    // Show start screen
    pauseScreen.style.display = 'none';
    gameUI.style.display = 'none';
    pauseBtn.style.display = 'none';
    startScreen.style.display = 'flex';
}

// Event listeners
startBtn.addEventListener('click', startGame);
pauseBtn.addEventListener('click', pauseGame);
resumeBtn.addEventListener('click', resumeGame);
restartBtn.addEventListener('click', restartGame);

// 创建手掌轮廓显示
function createHandOutline(landmarks) {
    // 清除现有的手掌轮廓
    slingshotBodies.forEach(body => World.remove(engine.world, body));
    slingshotConstraints.forEach(constraint => World.remove(engine.world, constraint));
    slingshotBodies = [];
    slingshotConstraints = [];
    
    // 获取关键点
    const wrist = landmarks[0];
    const thumbTip = landmarks[4];
    const indexTip = landmarks[8];
    const middleTip = landmarks[12];
    const ringTip = landmarks[16];
    const pinkyTip = landmarks[20];
    
    // 计算手掌中心
    const centerX = (wrist.x + thumbTip.x + indexTip.x + middleTip.x + ringTip.x + pinkyTip.x) / 6 * canvasWidth;
    const centerY = (wrist.y + thumbTip.y + indexTip.y + middleTip.y + ringTip.y + pinkyTip.y) / 6 * canvasHeight;
    
    // 计算抓取半径
    const handSize = Math.sqrt(
        Math.pow((thumbTip.x - pinkyTip.x) * canvasWidth, 2) + 
        Math.pow((thumbTip.y - pinkyTip.y) * canvasHeight, 2)
    );
    const catchRadius = Math.max(handSize * 0.8, 60);
    
    // 创建一个圆形的抓取区域显示
    const numPoints = 16;
    for (let i = 0; i < numPoints; i++) {
        const angle = (i / numPoints) * Math.PI * 2;
        const x = centerX + Math.cos(angle) * catchRadius;
        const y = centerY + Math.sin(angle) * catchRadius;
        
        const pointBody = Bodies.circle(x, y, 3, {
            isStatic: true,
            label: 'handOutline',
            isSensor: true, // 不参与物理碰撞
            render: { fillStyle: '#00ff88' }
        });
        
        World.add(engine.world, pointBody);
        slingshotBodies.push(pointBody);
    }
}

// MediaPipe Hands result callback
function onResults(results) {
    const allBodies = Composite.allBodies(engine.world);
    
    // 始终显示摄像头画面，但只在游戏进行时处理游戏逻辑
    if (gameState !== 'playing') {
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        ctx.drawImage(results.image, 0, 0, canvasWidth, canvasHeight);
        return;
    }

    // 基于手部关键点更新手势识别
    if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
        const landmarks = results.multiHandLandmarks[0];
        
        // 获取所有重要的手部关键点
        const wrist = landmarks[0];           // WRIST
        const thumbTip = landmarks[4];        // THUMB_TIP
        const indexTip = landmarks[8];        // INDEX_FINGER_TIP
        const middleTip = landmarks[12];      // MIDDLE_FINGER_TIP
        const ringTip = landmarks[16];        // RING_FINGER_TIP
        const pinkyTip = landmarks[20];       // PINKY_TIP
        
        // 计算手掌中心位置（更准确的手的位置）
        const handCenterX = (wrist.x + thumbTip.x + indexTip.x + middleTip.x + ringTip.x + pinkyTip.x) / 6 * canvasWidth;
        const handCenterY = (wrist.y + thumbTip.y + indexTip.y + middleTip.y + ringTip.y + pinkyTip.y) / 6 * canvasHeight;
        
        // 计算手掌大小（用于确定抓取范围）
        const handSize = Math.sqrt(
            Math.pow((thumbTip.x - pinkyTip.x) * canvasWidth, 2) +
            Math.pow((thumbTip.y - pinkyTip.y) * canvasHeight, 2)
        );
        const catchRadius = Math.max(handSize * 0.8, 60); // 最小60像素的抓取半径

        // 计算手势速度
        if (lastHandPosition) {
            handVelocity.x = (handCenterX - lastHandPosition.x) * 1.5;
            handVelocity.y = (handCenterY - lastHandPosition.y) * 1.5;
        }
        lastHandPosition = { x: handCenterX, y: handCenterY };

        // 改进的握拳检测 - 检查所有指尖与手掌中心的距离
        const fingerDistances = [
            Math.sqrt(Math.pow((thumbTip.x - handCenterX/canvasWidth), 2) + Math.pow((thumbTip.y - handCenterY/canvasHeight), 2)),
            Math.sqrt(Math.pow((indexTip.x - handCenterX/canvasWidth), 2) + Math.pow((indexTip.y - handCenterY/canvasHeight), 2)),
            Math.sqrt(Math.pow((middleTip.x - handCenterX/canvasWidth), 2) + Math.pow((middleTip.y - handCenterY/canvasHeight), 2)),
            Math.sqrt(Math.pow((ringTip.x - handCenterX/canvasWidth), 2) + Math.pow((ringTip.y - handCenterY/canvasHeight), 2)),
            Math.sqrt(Math.pow((pinkyTip.x - handCenterX/canvasWidth), 2) + Math.pow((pinkyTip.y - handCenterY/canvasHeight), 2))
        ];
        
        // 如果大部分手指都收拢了，就认为是握拳
        const closedFingers = fingerDistances.filter(dist => dist < 0.12).length;
        handClosed = closedFingers >= 3; // 至少3个手指收拢

        // 清除现有弹弓
        slingshotBodies.forEach(body => World.remove(engine.world, body));
        slingshotConstraints.forEach(constraint => World.remove(engine.world, constraint));
        slingshotBodies = [];
        slingshotConstraints = [];
        
        if (handClosed) {
            // 在手掌范围内寻找所有可抓取的星星
            const stars = allBodies.filter(b => b.label === 'star');
            const availableStars = [];
            
            stars.forEach(star => {
                const distance = Math.sqrt(
                    Math.pow(star.position.x - handCenterX, 2) +
                    Math.pow(star.position.y - handCenterY, 2)
                );
                
                // 检查星星是否在抓取范围内且尚未被抓住
                if (distance < catchRadius && !caughtStars.find(c => c.starId === star.starId)) {
                    availableStars.push({ star, distance });
                }
            });
            
            // 按距离排序，优先抓取最近的星星
            availableStars.sort((a, b) => a.distance - b.distance);
            
            // 抓取星星，直到达到最大数量
            availableStars.forEach(({ star }) => {
                if (caughtStars.length < maxCaughtStars) {
                    caughtStars.push(star);
                    Body.setStatic(star, true);
                    starManager.catchStar(star);
                }
            });
            
        } else { // 手张开
            if (caughtStars.length > 0) {
                // 投掷所有抓住的星星
                caughtStars.forEach((star, index) => {
                    Body.setStatic(star, false);
                    
                    // 为每个星星添加稍微不同的投掷速度，形成散开效果
                    const spreadAngle = (index - caughtStars.length / 2) * 0.3; // 散开角度
                    const velocityX = handVelocity.x + Math.cos(spreadAngle) * 50;
                    const velocityY = handVelocity.y + Math.sin(spreadAngle) * 50;
                    
                    Body.setVelocity(star, {
                        x: velocityX,
                        y: velocityY
                    });
                    Body.setAngularVelocity(star, (Math.random() - 0.5) * 0.5);
                });
                
                caughtStars = []; // 清空抓住的星星数组
            }
            // 创建更自然的手掌轮廓显示
            createHandOutline(landmarks);
        }
    } else {
        // 没有检测到手势，释放所有星星并清理
        if (caughtStars.length > 0) {
            caughtStars.forEach(star => {
                Body.setStatic(star, false);
            });
            caughtStars = [];
        }
        handClosed = false;
        lastHandPosition = null;
        slingshotBodies.forEach(body => World.remove(engine.world, body));
        slingshotConstraints.forEach(constraint => World.remove(engine.world, constraint));
        slingshotBodies = [];
        slingshotConstraints = [];
    }

    // 更新所有抓住的星星位置，让它们跟随手的移动
    if (caughtStars.length > 0 && lastHandPosition) {
        caughtStars.forEach((star, index) => {
            // 为每个星星添加稍微不同的偏移，避免重叠
            const offsetAngle = (index / caughtStars.length) * Math.PI * 2;
            const offsetRadius = Math.min(15, caughtStars.length * 3); // 根据星星数量调整半径
            const offsetX = Math.cos(offsetAngle) * offsetRadius;
            const offsetY = Math.sin(offsetAngle) * offsetRadius;
            
            Body.setPosition(star, {
                x: lastHandPosition.x + offsetX,
                y: lastHandPosition.y + offsetY
            });
        });
    }

    // Remove fallen stars
    starManager.removeFallenStars();

    // Draw everything
    drawGame(results, allBodies);
}

// Draw game screen
function drawGame(results, allBodies) {
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    ctx.drawImage(results.image, 0, 0, canvasWidth, canvasHeight);
    
    // Draw stars
    allBodies.forEach(body => {
        if (body.label === 'star') {
            ctx.save();
            ctx.translate(body.position.x, body.position.y);
            ctx.rotate(body.angle);
            ctx.beginPath();
            // Draw a 5-point star
            const outerRadius = 20;
            const innerRadius = 10;
            for (let i = 0; i < 10; i++) {
                const radius = i % 2 === 0 ? outerRadius : innerRadius;
                const angle = (i * Math.PI) / 5;
                ctx.lineTo(Math.cos(angle) * radius, Math.sin(angle) * radius);
            }
            ctx.closePath();

            // 特殊效果
            const isCaught = caughtStars.some(s => s.starId === body.starId);
            const isInHand = starsInHand.some(s => s.body.starId === body.starId);
            
            if (isCaught) {
                // 正在被抓住的星星 - 亮黄色高亮
                ctx.fillStyle = '#FFFF00';
                ctx.strokeStyle = '#FFD700';
                ctx.shadowColor = '#FFFF00';
                ctx.shadowBlur = 30;
            } else if (isInHand) {
                // 手上的星星但没有被抓住 - 粉色
                ctx.fillStyle = '#FF69B4';
                ctx.strokeStyle = '#FF1493';
                ctx.shadowColor = '#FF69B4';
                ctx.shadowBlur = 20;
            } else {
                // 自由掉落的星星 - 淡黄色
                ctx.fillStyle = '#FFEB3B';
                ctx.strokeStyle = '#FFC107';
                ctx.shadowColor = '#FFEB3B';
                ctx.shadowBlur = 15;
            }
            
            ctx.lineWidth = 2;
            ctx.fill();
            ctx.stroke();
            ctx.restore();
        }
    });

    // 绘制手掌抓取区域
    if (slingshotBodies.length > 0 && !handClosed) {
        ctx.save();
        
        // 绘制抓取范围圆圈
        if (slingshotBodies.length > 0) {
            // 计算圆心
            const centerX = slingshotBodies.reduce((sum, body) => sum + body.position.x, 0) / slingshotBodies.length;
            const centerY = slingshotBodies.reduce((sum, body) => sum + body.position.y, 0) / slingshotBodies.length;
            
            // 计算半径
            const radius = Math.sqrt(
                Math.pow(slingshotBodies[0].position.x - centerX, 2) +
                Math.pow(slingshotBodies[0].position.y - centerY, 2)
            );
            
            // 绘制半透明的抓取区域
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(0, 255, 136, 0.1)';
            ctx.fill();
            
            // 绘制抓取区域边框
            ctx.strokeStyle = '#00ff88';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            ctx.shadowColor = '#00ff88';
            ctx.shadowBlur = 10;
            ctx.stroke();
            ctx.setLineDash([]); // 重置虚线
        }
        
        // 绘制圆圈上的点
        slingshotBodies.forEach(body => {
            ctx.beginPath();
            ctx.arc(body.position.x, body.position.y, 6, 0, Math.PI * 2);
            ctx.fillStyle = '#00ff88';
            ctx.shadowColor = '#00ff88';
            ctx.shadowBlur = 8;
            ctx.fill();
        });
        
        ctx.restore();
    }
    
    // 绘制手掌中心点（当握拳时）
    if (handClosed && lastHandPosition) {
        ctx.save();
        ctx.beginPath();
        ctx.arc(lastHandPosition.x, lastHandPosition.y, 15, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255, 215, 0, 0.6)';
        ctx.shadowColor = '#ffd700';
        ctx.shadowBlur = 20;
        ctx.fill();
        
        // 绘制握拳指示器
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        ctx.stroke();
        ctx.restore();
    }
}

// Initialize MediaPipe Hands
const hands = new Hands({locateFile: (file) => {
    return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
}});

hands.setOptions({
    maxNumHands: 1,
    modelComplexity: 1,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5
});

hands.onResults(onResults);

// Setup camera
canvas.width = canvasWidth;
canvas.height = canvasHeight;

const camera = new Camera(video, {
    onFrame: async () => {
        await hands.send({image: video});
    },
    width: canvasWidth,
    height: canvasHeight
});

// Start everything
camera.start().then(() => {
    loading.style.display = 'none';
    
    // Start physics engine
    runner = Runner.create();
    Runner.run(runner, engine);
    
}).catch(err => {
    console.error(err);
    loading.textContent = '发生错误，请检查摄像头权限并刷新页面。';
}); 