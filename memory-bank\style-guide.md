# 样式指南

## 1. HTML 规范

*   **语义化标签:** 始终优先使用语义化HTML标签 (e.g., `<article>`, `<aside>`, `<nav>`, `<header>`, `<footer>`, `<main>`, `<section>`) 来构建清晰的文档结构。
*   **可访问性 (A11y):**
    *   为所有重要的图像添加描述性的 `alt` 属性。
    *   确保表单元素有正确的 `<label>` 关联。
    *   使用 ARIA 属性 (在必要时) 来增强非标准交互元素的可访问性。
*   **代码格式:**
    *   使用 Prettier 进行自动格式化。
    *   缩进：使用2个空格。
    *   属性顺序：按逻辑分组或按字母顺序。
    *   标签闭合：所有非自闭合标签必须闭合。

## 2. CSS 规范

*   **命名约定:**
    *   **BEM (Block, Element, Modifier):** 对于组件化的CSS，推荐使用BEM命名约定。
        *   示例: `.game-card__title--highlighted`
    *   对于通用工具类，可以使用简短的、有意义的名称 (e.g., `.text-center`, `.hidden`)。
*   **代码格式:**
    *   使用 Prettier 进行自动格式化。
    *   缩进：使用2个空格。
    *   每个声明占一行。
    *   在规则集之间留一个空行。
    *   颜色值：优先使用十六进制 (e.g., `#333`) 或 `rgba()` (需要透明度时)。
*   **单位:**
    *   字体大小: 优先使用 `rem` (根元素 `<html>` 字体大小为基准) 或 `em`。
    *   间距 (padding, margin): 使用 `px` 或 `rem`。
    *   布局: `vw`, `vh`, `%` 或 `flex`/`grid` 单位。
*   **CSS变量 (Custom Properties):**
    *   对于主题色、常用间距、字体栈等，使用CSS变量定义在 `:root` 中，方便维护和主题切换。
    *   示例:
        ```css
        :root {
            --primary-color: #3498db;
            --base-font-size: 16px;
            --spacing-unit: 8px;
        }
        body {
            font-size: var(--base-font-size);
            color: var(--primary-color);
        }
        .container {
            padding: calc(var(--spacing-unit) * 2); /* 16px */
        }
        ```
*   **模块化:**
    *   将样式按组件或功能划分到不同的 `.css` 文件中 (如果项目规模增大，考虑Sass/SCSS)。
    *   门户的全局样式在 `css/style.css`。
    *   每个游戏可以有自己的独立样式表，通过 `<iframe>` 的 `srcdoc` 或链接到其内部HTML。

## 3. JavaScript 规范

*   **ECMAScript 版本:** 使用 ES6+ 新特性 (e.g., `let`, `const`,箭头函数, 类, 模块, Promises, async/await)。
*   **代码风格与格式化:**
    *   **ESLint + Prettier:** 强制使用 ESLint 进行代码风格检查和 Prettier 进行自动格式化。相关配置文件 (`.eslintrc.js`, `.prettierrc.js`) 应提交到版本库。
    *   **命名约定:**
        *   变量和函数名: `camelCase` (e.g., `gameScore`, `loadGameAssets`)。
        *   类名: `PascalCase` (e.g., `Player`, `GameManager`)。
        *   常量: `UPPER_SNAKE_CASE` (e.g., `MAX_LIVES`, `API_ENDPOINT`)。
        *   私有属性/方法 (约定): 前缀下划线 `_` (e.g., `_privateVariable`, `_internalMethod`)。
    *   **缩进:** 使用2个空格。
    *   **分号:** 总是使用分号。
*   **模块化:**
    *   使用 ES6 模块 (`import`/`export`)。每个文件应只包含一个或一组高度相关的类/函数。
    *   示例:
        ```javascript
        // utils/collision.js
        export function checkCollision(rect1, rect2) {
            // ...
        }

        // game/player.js
        import { checkCollision } from '../utils/collision.js';
        export class Player {
            // ...
        }
        ```
*   **注释:**
    *   对复杂的逻辑、算法或不明显的代码段添加清晰的注释。
    *   使用 JSDoc 风格的注释为函数和类提供文档。
    *   示例:
        ```javascript
        /**
         * Loads all game assets.
         * @param {string[]} assetUrls - An array of URLs for the assets to load.
         * @returns {Promise<void>} A promise that resolves when all assets are loaded.
         */
        async function loadAssets(assetUrls) {
            // ...
        }
        ```
*   **DOM 操作:**
    *   缓存DOM元素的引用，避免重复查询。
    *   尽量减少直接的DOM操作，特别是在游戏循环中。
    *   对用户输入进行适当的清理和验证。
*   **异步操作:**
    *   优先使用 `async/await` 处理 Promise，使异步代码更易读。
    *   正确处理 Promise 的拒绝和错误。
*   **错误处理:**
    *   使用 `try...catch` 块捕获和处理潜在错误。
    *   向控制台提供有意义的错误信息。
*   **严格模式:** 在所有JS文件顶部或函数内部使用 `'use strict';`。ES6模块默认在严格模式下运行。

## 4. 文件与目录结构

*   参考项目初始化时建立的结构。
*   `css/`: 存放CSS文件。
    *   `style.css`: 门户主样式。
*   `js/`: 存放JavaScript文件。
    *   `script.js`: 门户主脚本。
    *   `libs/`: (可选) 存放第三方库。
    *   `components/`: (可选, 如有需要) 存放UI组件相关的JS。
    *   `utils/`: (可选) 存放通用工具函数模块。
*   `games/`: 存放各个独立游戏的目录。
    *   `games/[game-name]/`: 每个游戏一个子目录。
        *   `index.html`: 游戏主HTML文件。
        *   `js/`: 游戏相关的JS。
        *   `css/`: 游戏相关的CSS。
        *   `assets/`: 游戏资源 (图片、音频等)。
*   `assets/` (或 `img/`, `audio/` 在根目录): 存放门户共享的静态资源。
*   `memory-bank/`: 存放项目管理和设计文档。

## 5. 提交信息规范 (可选, 推荐)

*   遵循 Conventional Commits 规范。
*   示例: `feat: add player movement logic`
*   示例: `fix: resolve collision detection bug in pacman`
*   示例: `docs: update project brief with AI details`

此样式指南旨在提供一致性和可维护性。团队成员应遵循这些准则。随着项目的进展，本指南可能会被更新。 