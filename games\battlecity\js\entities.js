// 游戏实体系统
class Entity {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.active = true;
        this.id = Math.random().toString(36).substr(2, 9);
    }

    // 获取边界框
    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y,
            bottom: this.y + this.height
        };
    }

    // 检查碰撞
    collidesWith(other) {
        const a = this.getBounds();
        const b = other.getBounds();
        
        return !(a.right <= b.left || 
                a.left >= b.right || 
                a.bottom <= b.top || 
                a.top >= b.bottom);
    }

    // 获取中心点
    getCenter() {
        return {
            x: this.x + this.width / 2,
            y: this.y + this.height / 2
        };
    }

    update() {
        // 基类更新方法
    }

    render(ctx) {
        // 基类渲染方法
    }
}

// 坦克类
class Tank extends Entity {
    constructor(x, y, type = 'player') {
        super(x, y, 32, 32);
        this.type = type;
        this.direction = 0; // 0: 上, 1: 右, 2: 下, 3: 左
        this.speed = type === 'player' ? 2 : 1;
        this.health = type === 'player' ? 3 : 1;
        this.maxHealth = this.health;
        this.canShoot = true;
        this.shootCooldown = 0;
        this.maxShootCooldown = type === 'player' ? 300 : 600; // 毫秒
        this.lastShot = 0;
        
        // 移动相关
        this.moving = false;
        this.targetX = x;
        this.targetY = y;
        
        // 敌人AI相关
        this.aiTimer = 0;
        this.aiDirection = Math.floor(Math.random() * 4);
        this.aiShootTimer = 0;
        
        // 视觉效果
        this.animationFrame = 0;
        this.lastFrameTime = 0;
        
        // 皮肤和颜色
        this.skin = type === 'player' ? (window.gameStorage?.get('playerSkin') || 'classic') : 'enemy';
        this.colors = this.getSkinColors();
        
        // 强化属性
        this.powerups = {
            speed: 1,
            armor: 1,
            fireRate: 1,
            multiShot: false
        };
    }

    // 获取皮肤颜色
    getSkinColors() {
        const skins = {
            classic: { primary: '#4ecdc4', secondary: '#44a08d', accent: '#ffd23f' },
            military: { primary: '#2d5016', secondary: '#3d6b1f', accent: '#8b4513' },
            futuristic: { primary: '#667eea', secondary: '#764ba2', accent: '#ff6b35' },
            stealth: { primary: '#2c3e50', secondary: '#34495e', accent: '#e74c3c' },
            golden: { primary: '#f7931e', secondary: '#ffd23f', accent: '#ff6b35' },
            enemy: { primary: '#e74c3c', secondary: '#c0392b', accent: '#f39c12' }
        };
        
        return skins[this.skin] || skins.classic;
    }

    // 移动
    move(direction) {
        if (direction !== undefined) {
            this.direction = direction;
        }
        
        const moveDistance = this.speed * this.powerups.speed;
        let newX = this.x;
        let newY = this.y;
        
        switch (this.direction) {
            case 0: // 上
                newY -= moveDistance;
                break;
            case 1: // 右
                newX += moveDistance;
                break;
            case 2: // 下
                newY += moveDistance;
                break;
            case 3: // 左
                newX -= moveDistance;
                break;
        }
        
        // 边界检查
        if (newX >= 0 && newX <= 800 && newY >= 0 && newY <= 800) {
            this.x = newX;
            this.y = newY;
            this.moving = true;
            
            // 创建尘土效果
            if (window.effectsManager && Math.random() < 0.1) {
                const center = this.getCenter();
                window.effectsManager.createDustEffect(center.x, center.y);
            }
        }
    }

    // 射击
    shoot() {
        const now = Date.now();
        if (!this.canShoot || now - this.lastShot < this.maxShootCooldown / this.powerups.fireRate) {
            return null;
        }
        
        this.lastShot = now;
        const bullets = [];
        const center = this.getCenter();
        
        // 计算子弹起始位置
        let bulletX = center.x - 2;
        let bulletY = center.y - 2;
        
        switch (this.direction) {
            case 0: // 上
                bulletY = this.y - 4;
                break;
            case 1: // 右
                bulletX = this.x + this.width;
                break;
            case 2: // 下
                bulletY = this.y + this.height;
                break;
            case 3: // 左
                bulletX = this.x - 4;
                break;
        }
        
        // 普通子弹
        bullets.push(new Bullet(bulletX, bulletY, this.direction, this.type));
        
        // 多重射击
        if (this.powerups.multiShot) {
            const leftDir = (this.direction + 3) % 4;
            const rightDir = (this.direction + 1) % 4;
            
            bullets.push(new Bullet(bulletX, bulletY, leftDir, this.type));
            bullets.push(new Bullet(bulletX, bulletY, rightDir, this.type));
        }
        
        // 播放射击音效
        if (window.audioManager) {
            window.audioManager.playShoot();
        }
        
        return bullets;
    }

    // 受到伤害
    takeDamage(damage = 1) {
        const actualDamage = Math.max(1, damage - (this.powerups.armor - 1));
        this.health -= actualDamage;
        
        if (this.health <= 0) {
            this.active = false;
            
            // 创建爆炸效果
            if (window.effectsManager) {
                const center = this.getCenter();
                window.effectsManager.createExplosion(center.x, center.y, 1.5);
            }
            
            // 播放爆炸音效
            if (window.audioManager) {
                if (this.type === 'player') {
                    window.audioManager.playGameOver();
                } else {
                    window.audioManager.playEnemyDestroy();
                }
            }
            
            return true; // 被摧毁
        }
        
        // 播放击中音效
        if (window.audioManager) {
            window.audioManager.playHit();
        }
        
        // 创建击中效果
        if (window.effectsManager) {
            const center = this.getCenter();
            window.effectsManager.createHitEffect(center.x, center.y);
        }
        
        return false; // 未被摧毁
    }

    // 应用道具效果
    applyPowerup(powerupType, duration = 10000) {
        switch (powerupType) {
            case 'speed':
                this.powerups.speed = 1.5;
                setTimeout(() => { this.powerups.speed = 1; }, duration);
                break;
            case 'armor':
                this.powerups.armor = 2;
                setTimeout(() => { this.powerups.armor = 1; }, duration);
                break;
            case 'fireRate':
                this.powerups.fireRate = 2;
                setTimeout(() => { this.powerups.fireRate = 1; }, duration);
                break;
            case 'multiShot':
                this.powerups.multiShot = true;
                setTimeout(() => { this.powerups.multiShot = false; }, duration);
                break;
            case 'health':
                this.health = Math.min(this.maxHealth, this.health + 1);
                break;
        }
    }

    // 敌人AI更新
    updateAI() {
        if (this.type === 'player') return;
        
        const now = Date.now();
        
        // 移动AI
        this.aiTimer += 16; // 假设60fps
        if (this.aiTimer > 1000 + Math.random() * 2000) { // 1-3秒改变方向
            this.aiDirection = Math.floor(Math.random() * 4);
            this.aiTimer = 0;
        }
        
        // 尝试移动
        const oldX = this.x;
        const oldY = this.y;
        this.move(this.aiDirection);
        
        // 如果卡住了，改变方向
        if (this.x === oldX && this.y === oldY) {
            this.aiDirection = Math.floor(Math.random() * 4);
        }
        
        // 射击AI
        this.aiShootTimer += 16;
        if (this.aiShootTimer > 2000 + Math.random() * 3000) { // 2-5秒射击一次
            this.shoot();
            this.aiShootTimer = 0;
        }
    }

    update() {
        this.moving = false;
        
        if (this.type !== 'player') {
            this.updateAI();
        }
        
        // 更新动画帧
        const now = Date.now();
        if (now - this.lastFrameTime > 200) { // 200ms切换一帧
            this.animationFrame = (this.animationFrame + 1) % 2;
            this.lastFrameTime = now;
        }
    }

    render(ctx) {
        if (!this.active) return;
        
        ctx.save();
        
        // 移动到坦克中心
        const centerX = this.x + this.width / 2;
        const centerY = this.y + this.height / 2;
        ctx.translate(centerX, centerY);
        
        // 根据方向旋转
        ctx.rotate(this.direction * Math.PI / 2);
        
        // 绘制坦克主体
        ctx.fillStyle = this.colors.primary;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
        
        // 绘制坦克履带
        ctx.fillStyle = this.colors.secondary;
        ctx.fillRect(-this.width/2, -this.height/2 + 4, this.width, 4);
        ctx.fillRect(-this.width/2, this.height/2 - 8, this.width, 4);
        
        // 绘制炮管
        ctx.fillStyle = this.colors.accent;
        ctx.fillRect(-2, -this.height/2 - 8, 4, 12);
        
        // 绘制坦克中心
        ctx.fillStyle = this.colors.accent;
        ctx.beginPath();
        ctx.arc(0, 0, 6, 0, Math.PI * 2);
        ctx.fill();
        
        // 如果有护甲强化，绘制护盾效果
        if (this.powerups.armor > 1) {
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.arc(0, 0, this.width/2 + 4, 0, Math.PI * 2);
            ctx.stroke();
            ctx.setLineDash([]);
        }
        
        ctx.restore();
        
        // 绘制血量条（仅玩家）
        if (this.type === 'player' && this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 4;
            const barY = this.y - 8;
            
            // 背景
            ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
            ctx.fillRect(this.x, barY, barWidth, barHeight);
            
            // 血量
            ctx.fillStyle = '#4ecdc4';
            const healthWidth = (this.health / this.maxHealth) * barWidth;
            ctx.fillRect(this.x, barY, healthWidth, barHeight);
        }
    }
}

// 子弹类
class Bullet extends Entity {
    constructor(x, y, direction, owner) {
        super(x, y, 4, 4);
        this.direction = direction;
        this.owner = owner;
        this.speed = 4;
        this.damage = 1;
        this.color = owner === 'player' ? '#ffd23f' : '#ff6b35';
        
        // 计算速度向量
        this.vx = 0;
        this.vy = 0;
        
        switch (direction) {
            case 0: // 上
                this.vy = -this.speed;
                break;
            case 1: // 右
                this.vx = this.speed;
                break;
            case 2: // 下
                this.vy = this.speed;
                break;
            case 3: // 左
                this.vx = -this.speed;
                break;
        }
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        
        // 边界检查
        if (this.x < 0 || this.x > 832 || this.y < 0 || this.y > 832) {
            this.active = false;
        }
        
        // 创建轨迹效果
        if (window.effectsManager && Math.random() < 0.3) {
            window.effectsManager.createBulletTrail(
                this.x + this.width/2, 
                this.y + this.height/2, 
                this.direction * Math.PI / 2,
                this.color
            );
        }
    }

    render(ctx) {
        if (!this.active) return;
        
        ctx.save();
        ctx.fillStyle = this.color;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 5;
        
        // 绘制子弹
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, this.y + this.height/2, this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
    }

    // 击中目标
    hit() {
        this.active = false;
        
        // 创建击中效果
        if (window.effectsManager) {
            const center = this.getCenter();
            window.effectsManager.createHitEffect(center.x, center.y, this.color);
        }
    }
}

// 墙壁类
class Wall extends Entity {
    constructor(x, y, type = 'brick') {
        super(x, y, 32, 32);
        this.type = type; // 'brick', 'steel', 'water', 'grass'
        this.destructible = type === 'brick';
        this.passable = type === 'grass';
        this.health = type === 'brick' ? 1 : (type === 'steel' ? 3 : 1);
        this.maxHealth = this.health;
        
        this.colors = this.getTypeColors();
    }

    getTypeColors() {
        const colors = {
            brick: { primary: '#8B4513', secondary: '#A0522D', accent: '#CD853F' },
            steel: { primary: '#708090', secondary: '#778899', accent: '#B0C4DE' },
            water: { primary: '#4682B4', secondary: '#5F9EA0', accent: '#87CEEB' },
            grass: { primary: '#228B22', secondary: '#32CD32', accent: '#90EE90' }
        };
        
        return colors[this.type] || colors.brick;
    }

    takeDamage(damage = 1) {
        if (!this.destructible) return false;
        
        this.health -= damage;
        
        if (this.health <= 0) {
            this.active = false;
            
            // 创建墙壁破坏效果
            if (window.effectsManager) {
                const center = this.getCenter();
                window.effectsManager.createExplosion(center.x, center.y, 0.8, this.colors.primary);
            }
            
            // 播放墙壁破坏音效
            if (window.audioManager) {
                window.audioManager.playComplexSound('wallDestroy');
            }
            
            return true; // 被摧毁
        }
        
        return false; // 未被摧毁
    }

    render(ctx) {
        if (!this.active) return;
        
        ctx.save();
        
        switch (this.type) {
            case 'brick':
                this.renderBrick(ctx);
                break;
            case 'steel':
                this.renderSteel(ctx);
                break;
            case 'water':
                this.renderWater(ctx);
                break;
            case 'grass':
                this.renderGrass(ctx);
                break;
        }
        
        ctx.restore();
    }

    renderBrick(ctx) {
        // 绘制砖块纹理
        ctx.fillStyle = this.colors.primary;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 砖块线条
        ctx.strokeStyle = this.colors.secondary;
        ctx.lineWidth = 1;
        
        // 水平线
        for (let i = 1; i < 4; i++) {
            const y = this.y + (this.height / 4) * i;
            ctx.beginPath();
            ctx.moveTo(this.x, y);
            ctx.lineTo(this.x + this.width, y);
            ctx.stroke();
        }
        
        // 垂直线（交错）
        for (let i = 1; i < 4; i++) {
            const y = this.y + (this.height / 4) * i;
            const offset = (i % 2) * (this.width / 2);
            const x = this.x + this.width / 4 + offset;
            
            ctx.beginPath();
            ctx.moveTo(x, y - this.height / 4);
            ctx.lineTo(x, y);
            ctx.stroke();
        }
    }

    renderSteel(ctx) {
        // 绘制钢铁纹理
        ctx.fillStyle = this.colors.primary;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 金属光泽
        const gradient = ctx.createLinearGradient(this.x, this.y, this.x + this.width, this.y + this.height);
        gradient.addColorStop(0, this.colors.accent);
        gradient.addColorStop(0.5, this.colors.primary);
        gradient.addColorStop(1, this.colors.secondary);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 钢铁边框
        ctx.strokeStyle = this.colors.secondary;
        ctx.lineWidth = 2;
        ctx.strokeRect(this.x, this.y, this.width, this.height);
    }

    renderWater(ctx) {
        // 绘制水面效果
        const time = Date.now() * 0.005;
        
        ctx.fillStyle = this.colors.primary;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 水波纹效果
        ctx.save();
        ctx.globalAlpha = 0.3;
        
        for (let i = 0; i < 3; i++) {
            const waveY = this.y + this.height * (0.3 + i * 0.2) + Math.sin(time + i) * 2;
            ctx.fillStyle = this.colors.accent;
            ctx.fillRect(this.x, waveY, this.width, 2);
        }
        
        ctx.restore();
    }

    renderGrass(ctx) {
        // 绘制草地纹理
        ctx.fillStyle = this.colors.primary;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 草叶效果
        ctx.strokeStyle = this.colors.secondary;
        ctx.lineWidth = 1;
        
        for (let i = 0; i < 8; i++) {
            const x = this.x + (this.width / 8) * i + Math.random() * 4;
            const y1 = this.y + this.height;
            const y2 = this.y + this.height * 0.3 + Math.random() * this.height * 0.4;
            
            ctx.beginPath();
            ctx.moveTo(x, y1);
            ctx.lineTo(x + Math.random() * 4 - 2, y2);
            ctx.stroke();
        }
    }
}

// 基地类
class Base extends Entity {
    constructor(x, y) {
        super(x, y, 64, 64);
        this.health = 5;
        this.maxHealth = 5;
        this.type = 'base';
    }

    takeDamage(damage = 1) {
        this.health -= damage;
        
        if (this.health <= 0) {
            this.active = false;
            
            // 创建大爆炸效果
            if (window.effectsManager) {
                const center = this.getCenter();
                window.effectsManager.createExplosion(center.x, center.y, 2.5);
                window.effectsManager.screenShake(15);
            }
            
            // 播放基地被摧毁音效
            if (window.audioManager) {
                window.audioManager.playBaseHit();
            }
            
            return true; // 基地被摧毁
        }
        
        // 播放基地受损音效
        if (window.audioManager) {
            window.audioManager.playHit();
        }
        
        // 创建击中效果
        if (window.effectsManager) {
            const center = this.getCenter();
            window.effectsManager.createHitEffect(center.x, center.y, '#ff6b35');
        }
        
        return false;
    }

    render(ctx) {
        if (!this.active) return;
        
        ctx.save();
        
        // 基地主体
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // 基地装饰
        ctx.fillStyle = '#34495e';
        ctx.fillRect(this.x + 8, this.y + 8, this.width - 16, this.height - 16);
        
        // 基地核心
        ctx.fillStyle = '#e74c3c';
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, this.y + this.height/2, 12, 0, Math.PI * 2);
        ctx.fill();
        
        // 基地血量指示器
        const healthRatio = this.health / this.maxHealth;
        ctx.fillStyle = healthRatio > 0.6 ? '#2ecc71' : (healthRatio > 0.3 ? '#f39c12' : '#e74c3c');
        
        for (let i = 0; i < this.maxHealth; i++) {
            const x = this.x + 8 + i * 10;
            const y = this.y - 8;
            
            if (i < this.health) {
                ctx.fillRect(x, y, 8, 4);
            } else {
                ctx.strokeStyle = ctx.fillStyle;
                ctx.strokeRect(x, y, 8, 4);
            }
        }
        
        ctx.restore();
    }
}
