# 旅行规划Agent - 工作流程与设计文档

本文档旨在定义一个集成了高德地图（Amap）MCP工具集、具备用户记忆和个性化能力的智能旅行规划Agent。Agent的目标是理解用户的复杂、口语化的旅行需求，通过调用外部工具获取实时信息，并结合用户偏好，最终生成结构化、可执行的旅行计划。

---

### 1. Agent 定位与目标

- **定位**: 一个懂旅行、懂用户的智能规划专家。
- **核心目标**:
    1.  **意图精准理解**: 准确解析用户输入，识别出发点、目的地、时间、预算、成员、兴趣点（如"亲子"、"网红打卡地"）等信息。
    2.  **个性化推荐**: 结合用户的历史行为、画像标签（存储于MongoDB中），提供千人千面的规划结果。
    3.  **动态信息整合**: 实时调用高德地图API获取天气、路况、POI信息，确保计划的时效性和准确性。
    4.  **结构化输出**: 生成标准化的JSON格式行程单，与前端（Web/小程序）解耦，易于渲染和二次开发。
    5.  **持续学习**: 将每次成功的规划和用户反馈存储为记忆，不断优化未来的推荐逻辑。

---

### 2. 核心能力：工具集（Amap MCP Tools）

Agent的能力来自于对以下15个高德API工具的编排和调用。

| 分类 | 工具名称 | 核心功能与用途 |
| :--- | :--- | :--- |
| **基础定位与地址解析** | `maps_geo` | **地址 → 经纬度**：所有空间计算的基础，流程的起点。 |
| | `maps_regeocode` | **经纬度 → 地址**：用于丰富地点信息，提供可读地址。 |
| | `maps_ip_location` | **IP地址 → 城市**：当用户未明确指定城市时，用于猜测默认城市。 |
| **路线规划与距离计算** | `maps_direction_driving` | **驾车路线规划**：用于规划城际、市内自驾路线。 |
| | `maps_direction_walking` | **步行路线规划**：用于景点之间、停车场到景点的短途路线。 |
| | `maps_direction_bicycling` | **骑行路线规划**：适用于城市慢行、景区游览。 |
| | `maps_direction_transit_integrated` | **公共交通路线规划**：适用于公共交通出行的用户。 |
| | `maps_distance` | **距离测量**：轻量级计算，用于快速筛选POI。 |
| **信息检索与周边搜索** | `maps_text_search` | **关键字搜索(POI)**：根据用户提到的特定名称搜索地点。 |
| | `maps_around_search` | **周边搜索(POI)**：**Agent核心工具**，用于动态发现目的的周边的美食、景点、停车场等。|
| | `maps_search_detail` | **POI 详情查询**：获取特定POI的评分、图片、营业时间等丰富信息。|
| **辅助上下文信息** | `maps_weather` | **天气查询**：决策行程安排的关键依据（如雨天推荐室内活动）。 |
| **高德App集成与唤醒** | `maps_schema_personal_map` | **生成行程地图链接**：将完整行程打包成一个高德地图链接。 |
| | `maps_schema_navi` | **生成导航链接**：为单段路程提供"一键导航"功能。 |
| | `maps_schema_take_taxi` | **生成打车链接**：提供便捷的出行方式。 |

---

### 3. 核心工作流（The Core Workflow）

Agent的工作流被设计为一个四阶段的、集成了数据库交互的闭环流程。

```mermaid
flowchart TD
    A["用户输入<br/>周末带孩子从亦庄开车去故宫"] 
    
    A --> B["Phase 1: 意图理解与个性化融合"]
    B --> B_DB[("读取用户数据库<br/>MongoDB")]
    B_DB --> B1["构建个性化查询指令"]
    
    B1 --> C["Phase 2: 动态工具规划与并行执行"]
    
    %% 第一层并行调用
    C --> L1["第一层并行调用 (基础信息层)"]
    L1 --> L1_1["maps_geo(起点)"]
    L1 --> L1_2["maps_geo(目的地)"]  
    L1 --> L1_3["maps_weather(目的地城市)"]
    
    L1_1 --> WAIT1["等待基础信息返回"]
    L1_2 --> WAIT1
    L1_3 --> WAIT1
    
    %% 第二层并行调用
    WAIT1 --> L2["第二层并行调用 (核心POI信息层)"]
    
    L2 --> L2_PARK["🅿️ 停车信息组"]
    L2_PARK --> L2_1["maps_around_search(停车场)"]
    L2_PARK --> L2_2["maps_search_detail(停车详情)"]
    
    L2 --> L2_CHARGE["🔋 充电信息组"]
    L2_CHARGE --> L2_3["maps_around_search(充电桩)"]
    L2_CHARGE --> L2_4["maps_search_detail(充电详情)"]
    
    L2 --> L2_FOOD["🍽️ 美食信息组"]
    L2_FOOD --> L2_5["maps_around_search(美食/当地特色)"]
    L2_FOOD --> L2_6["maps_around_search(用户偏好餐厅)"]
    L2_FOOD --> L2_7["maps_search_detail(餐厅详情)"]
    
    L2 --> L2_ATTR["🎯 景点信息组"]
    L2_ATTR --> L2_8["maps_around_search(景点)"]
    L2_ATTR --> L2_9["maps_search_detail(景点详情)"]
    
    L2 --> L2_HOTEL["🏨 住宿信息组"]
    L2_HOTEL --> L2_10["maps_around_search(酒店/民宿)"]
    L2_HOTEL --> L2_11["maps_search_detail(住宿详情)"]
    
    L2_1 --> WAIT2["等待核心POI信息返回"]
    L2_2 --> WAIT2
    L2_3 --> WAIT2
    L2_4 --> WAIT2
    L2_5 --> WAIT2
    L2_6 --> WAIT2
    L2_7 --> WAIT2
    L2_8 --> WAIT2
    L2_9 --> WAIT2
    L2_10 --> WAIT2
    L2_11 --> WAIT2
    
    %% 第三层并行调用
    WAIT2 --> L3["第三层并行调用 (路线与辅助信息层)"]
    
    L3 --> L3_ROUTE["🛣️ 路线规划组"]
    L3_ROUTE --> L3_1["maps_direction_driving(主路线)"]
    L3_ROUTE --> L3_2["maps_direction_walking(步行接驳)"]
    
    L3 --> L3_MEDIA["📸 图片媒体组"]
    L3_MEDIA --> L3_3["高质量POI图片获取"]
    
    L3_1 --> WAIT3["等待路线信息返回"]
    L3_2 --> WAIT3
    L3_3 --> WAIT3
    
    %% 第四层并行调用
    WAIT3 --> L4["第四层并行调用 (深度信息挖掘层)"]
    
    L4 --> L4_PERSONAL["🎯 个性化深度挖掘"]
    L4_PERSONAL --> L4_1["maps_around_search(个性化关键词)"]
    
    L4 --> L4_EMERGENCY["🚨 应急便民信息"]
    L4_EMERGENCY --> L4_2["maps_around_search(医院/药店/厕所)"]
    
    L4_1 --> WAIT4["等待深度信息返回"]
    L4_2 --> WAIT4
    
    %% Phase 3 决策分析
    WAIT4 --> F["Phase 3: 数据综合与智能决策"]
    F --> F1["天气影响分析<br/>雨天/高温应对"]
    F --> F2["POI综合评分模型<br/>停车场/餐厅/景点评分"]
    F --> F3["行程动态编排<br/>智能匹配+时间优化"]
    F --> F4["方案生成与预案<br/>Plan B + 旅行小贴士"]
    
    F1 --> G["Phase 4: 结构化结果生成与记忆存储"]
    F2 --> G
    F3 --> G
    F4 --> G
    
    G --> G1["生成结构化JSON行程单"]
    G --> G2["生成高德地图链接<br/>personal_map + navi"]
    G --> G3["写入记忆数据库<br/>trips + memories"]
    
    G3 --> G_DB[("MongoDB<br/>数据持久化")]
    
    G1 --> H["输出: 完整旅行规划<br/>JSON + 地图链接 + 小贴士"]
    G2 --> H
    G_DB --> H
    
    %% 样式定义
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style L1 fill:#fff9c4
    style L2 fill:#f0f4c3
    style L3 fill:#e8f5e8
    style L4 fill:#dcedc8
    style F fill:#fff3e0
    style G fill:#fce4ec
    style H fill:#e0f2f1
    
    %% 组样式
    style L2_PARK fill:#ffecb3
    style L2_CHARGE fill:#c8e6c9
    style L2_FOOD fill:#ffcdd2
    style L2_ATTR fill:#d1c4e9
    style L2_HOTEL fill:#b3e5fc
```

#### **Phase 1: 意图理解与个性化融合 (Intent Parsing & Personalization)**

-   **目标**: 将模糊的自然语言转化为结构化的、带有用户偏好的查询指令。
-   **步骤**:
    1.  **实体抽取**: 从用户输入中提取核心实体：
        -   `出发点`: 亦庄
        -   `目的地`: 故宫
        -   `出行方式`: 开车
        -   `时间`: 周末 (解析为具体日期)
        -   `修饰词/偏好`: "带孩子" -> `亲子`, "好停车" -> `停车场`, "小孩吃的餐厅" -> `美食`
    2.  **用户画像加载**: 根据`UserID`，从MongoDB的`users`和`memories`集合中读取用户数据。
        -   `历史偏好`: 之前是否去过类似景点？是否偏爱自然风光或历史古迹？
        -   `消费习惯`: 预算是经济型还是豪华型？
        -   `标签`: `旅游搭子`信息，家庭成员（有儿童）等。
    3.  **查询指令构建**: 将提取的实体和用户画像融合，形成内部查询对象。
        ```json
        {
          "origin": "北京亦庄",
          "destination": "北京故宫",
          "transportMode": "driving",
          "dates": ["2025-06-21", "2025-06-22"],
          "userProfile": { "has_children": true, "budget": "medium" },
          "interests": [
            { "type": "parking", "priority": 1 },
            { "type": "food", "tags": ["亲子餐厅", "儿童餐"], "priority": 2 },
            { "type": "attraction", "tags": ["适合儿童", "室内"], "priority": 3 }
          ]
        }
        ```

#### **Phase 2: 动态工具规划与并行执行 (业务维度扩展)**

-   **目标**: 基于内部查询指令，智能规划需要调用的工具组合，并以最高效、最全面的方式（并行）执行，为后续决策提供丰富的数据支持。
-   **策略**: 采用分层并行调用的策略，确保逻辑清晰和执行高效。

**第一层并行调用 (基础信息层)**
*获取规划所必需的起点、终点坐标和天气。*
- `maps_geo(address="起点")`
- `maps_geo(address="目的地")`
- `maps_weather(city="目的地城市")`

**第二层并行调用 (核心POI信息层)**
*基于第一层返回的目的地坐标，并发获取目的地周边的五大核心要素：*

-   **🅿️ 停车信息组**:
    -   `maps_around_search(location=dest_loc, keywords="停车场")`
    -   `maps_around_search(location=dest_loc, keywords="地下停车场")`
    -   对每个返回的停车场POI，调用 `maps_search_detail` 获取：收费标准、营业时间、到景点的步行距离、用户评分。

-   **🔋 新能源车充电信息组**:
    -   `maps_around_search(location=dest_loc, keywords="充电桩")`
    -   对每个返回的充电站POI，调用 `maps_search_detail` 获取：充电价格、快/慢充、充电桩功率、实时可用状态。

-   **🍽️ 美食餐饮信息组**:
    -   `maps_around_search(location=dest_loc, keywords="美食 OR 当地特色")`
    -   `maps_around_search(location=dest_loc, keywords="[用户偏好标签，如'亲子餐厅']")`
    -   对每个返回的餐厅POI，调用 `maps_search_detail` 获取：菜品图片、人均消费、营业时间、用户评分、特色标签。

-   **🎯 景点与娱乐信息组**:
    -   `maps_around_search(location=dest_loc, keywords="景点 OR [用户偏好标签，如'博物馆']")`
    -   对每个返回的景点POI，调用 `maps_search_detail` 获取：高清图片、门票价格、开放时间、建议游玩时长。

-   **🏨 住宿信息组** (如果行程跨天):
    -   `maps_around_search(location=dest_loc, keywords="酒店 OR 民宿")`
    -   对每个返回的住宿POI，调用 `maps_search_detail` 获取：房间图片、价格区间、用户评分、停车政策。

**第三层并行调用 (路线与辅助信息层)**
*在获取核心POI后，开始规划具体路线，并汇总费用信息。*

-   **🛣️ 路线规划组**:
    -   `maps_direction_driving(origin_loc, dest_loc)` 获取主干路线。
    -   `maps_direction_walking(停车场_loc, 景点_loc)` 获取步行接驳路线。
    -   对驾车路线，解析返回结果获得：过路费明细、预估耗时、路况。

-   **📸 图片与媒体组**:
    -   在所有`maps_search_detail`调用中，优先获取高质量的官方图片和用户上传图片，确保每个POI都有视觉参考。

**第四层并行调用 (深度信息挖掘层)**
*进行更深层次的个性化挖掘和应急信息准备。*

-   **🎯 个性化深度挖掘组**:
    -   根据用户画像标签（如"亲子"、"摄影"），调用 `maps_around_search` 搜索特定关键词（如"母婴室"、"拍照打卡地"）。
-   **🚨 应急与便民信息组**:
    -   `maps_around_search(location=dest_loc, keywords="医院 OR 药店 OR 公共厕所")`

#### **Phase 3: 数据综合与智能决策 (Data Synthesis & Intelligent Reasoning)**

-   **目标**: Agent的"大脑"。将Phase 2返回的海量、零散的数据，结合用户偏好和常识规则，整合成一个逻辑自洽、高质量、人性化的旅行计划。
-   **决策逻辑示例**:

    -   **天气影响分析**:
        -   若`weather_info`预报有雨，则自动降低室外景点的推荐优先级，并增加"雨天备选"标签，提升室内景点的权重，如博物馆、购物中心。
        -   若预报高温，则在行程中自动插入"避暑"时段，推荐室内咖啡馆或商场。

    -   **POI筛选与评分 (建立综合评分模型)**:
        -   **停车场评分**: `Score = w1 * (1/价格) + w2 * (1/步行距离) + w3 * 评分`。(w为权重)
        -   **餐厅评分**: `Score = w1 * 评分 + w2 * (1/人均消费) + w3 * [标签匹配度] + w4 * [有无图片]`。
        -   **景点评分**: `Score = w1 * 评分 + w2 * [标签匹配度] + w3 * (1/门票价格)`。
        -   基于综合评分，为用户筛选出Top3的推荐。

    -   **行程动态编排**:
        -   **智能匹配**: 基于POI评分和地理位置，为每日核心景点自动匹配评分最高的停车场和午/晚餐地点。
        -   **动态时间分配**: 根据景点的`建议游玩时长`和`maps_direction`返回的`耗时`，合理规划每日时间线，并自动加入15-30分钟的冗余时间。
        -   **多日行程优化**: 对于多日游，Agent会综合考虑所有天的景点分布，使用算法优化住宿地点，使其位于后续几日行程的中心区域，减少每日的重复驾驶路程。

    -   **方案生成与预案**:
        -   **创建备选方案(Plan B)**: 除了主推荐路线，额外提供一个备选方案。例如，一个"网红打卡"路线和一个"悠闲漫步"路线。
        -   **生成"旅行小贴士"**: 基于获取的各类信息，自动生成实用贴士，如"XX景点建议提前在线预订门票"、"XX餐厅下午5点后可能需要排队"、"停车场入口在XX路"等。

#### **Phase 4: 结构化结果生成与记忆存储 (Structured Response Generation & Memory Storage)**

-   **目标**: 将Agent的智能决策转化为用户友好的最终产品，并构建持续学习的数据闭环。
-   **核心产出**:

**4.1 多格式结果生成**
-   **结构化JSON行程单**: 
    -   包含完整的日程安排、POI详情、费用明细、地图链接
    -   支持前端渲染的标准化数据格式
    -   内嵌图片URL、评分、营业时间等丰富信息
-   **可视化地图产品**:
    -   调用 `maps_schema_personal_map` 生成完整行程地图
    -   为每个关键路段调用 `maps_schema_navi` 生成一键导航链接
    -   为打车需求调用 `maps_schema_take_taxi` 生成打车链接
-   **用户友好的文档**:
    -   自动生成旅行攻略HTML页面（如您的天津攻略案例）
    -   包含天气、费用、小贴士、路线图的完整攻略
    -   支持分享和离线查看

**4.2 质量检验与优化**
-   **逻辑一致性检查**: 验证时间安排的合理性、路线的连贯性
-   **费用合理性验证**: 确保预算计算准确，价格信息时效性
-   **用户体验优化**: 根据用户画像调整内容展示顺序和重点

**4.3 智能记忆构建**
-   **行程记忆存储**:
    -   `trips`集合: 完整行程JSON + 用户反馈 + 执行效果
    -   `poi_preferences`集合: 用户对特定POI的偏好度量
    -   `route_efficiency`集合: 路线规划的实际效果反馈
-   **用户画像更新**:
    -   基于本次规划，更新用户的兴趣标签权重
    -   记录预算偏好、出行习惯的变化趋势
    -   构建用户的"旅行DNA"档案
-   **系统学习优化**:
    -   分析成功规划的共同特征，优化推荐算法
    -   识别失败案例的问题原因，改进决策逻辑
    -   建立POI质量评价的动态更新机制

---

### 4. 调用策略示例

基于业务复杂度和用户需求，Agent采用不同的工具调用策略：

#### **4.1 基础信息查询 (Level 1)**
*单一信息点查询，1-2个工具调用*

**示例**: "查一下北京今天的天气"
```
工具链: maps_weather(city="北京") 
响应: 直接返回天气信息，包含温度、降雨、建议穿衣指数
```

**示例**: "故宫博物院现在是否开放？"
```
工具链: maps_text_search(keywords="故宫博物院") -> maps_search_detail(id)
响应: 营业时间、门票价格、今日状态
```

#### **4.2 路线规划查询 (Level 2)**
*涉及地理计算，3-5个工具调用*

**示例**: "从三里屯开车到颐和园要多久？停车方便吗？"
```
工具链: 
- 并行: maps_geo("三里屯") + maps_geo("颐和园")
- maps_direction_driving(起点, 终点)
- maps_around_search(location=颐和园, keywords="停车场")
- maps_search_detail(停车场详情)
响应: 路线时间、费用、推荐停车场及收费标准
```

**示例**: "我在国贸，想坐地铁去王府井，最快路线是什么？"
```
工具链:
- 并行: maps_geo("国贸") + maps_geo("王府井")  
- maps_direction_transit_integrated(起点, 终点, city="北京", cityd="北京")
响应: 地铁换乘方案、用时、票价
```

#### **4.3 周边探索查询 (Level 3)**
*区域性需求，8-15个工具调用*

**示例**: "我在三里屯，附近有什么好吃的川菜馆？要有停车位的"
```
工具链:
- maps_geo("三里屯") 获取坐标
- 并行调用:
  - maps_around_search(location, keywords="川菜")
  - maps_around_search(location, keywords="停车场", radius=500m)
- 对top5川菜馆并行调用 maps_search_detail 获取详情
- 计算各餐厅到停车场的 maps_distance
响应: 推荐3家川菜馆 + 最佳停车方案 + 步行路线
```

#### **4.4 全方位旅行规划 (Level 4)**
*复合需求，20-40个工具调用，四层并行架构*

**示例**: "周末带孩子从亦庄开车去故宫，想玩一整天，求推荐路线"
```
工具链: 完整四阶段工作流
- Phase 1: 意图解析 + 用户画像加载
- Phase 2: 四层并行调用
  * 第一层: 基础信息(3个调用)
  * 第二层: 五大业务组并行(15-20个调用)
  * 第三层: 路线规划组(5-8个调用)  
  * 第四层: 个性化挖掘(3-5个调用)
- Phase 3: 综合决策与智能匹配
- Phase 4: 多格式结果生成
响应: 完整一日游攻略 + 高德地图链接 + 费用明细 + 实用贴士
```

#### **4.5 跨城市复杂规划 (Level 5)**
*最高复杂度，50+工具调用*

**示例**: "五一假期从北京自驾去天津2天1夜游，求详细攻略"
```
工具链: 超级工作流
- 多城市并行信息获取:
  * 北京: 出发准备信息(天气、路况、充电站)
  * 天津: 全套旅游资源(景点、美食、住宿、停车)
  * 路线: 跨城驾车路线 + 城内接驳路线
- 二日行程优化算法:
  * 第一天: 到达 -> 游览 -> 住宿
  * 第二天: 继续游览 -> 返程
- 预案与应急:
  * Plan B方案(天气变化)
  * 应急信息(医院、救援)
响应: 详细2日攻略HTML + 每日导航链接 + 费用预算 + 应急预案
```

#### **4.6 智能调用优化策略**

-   **并行度动态调整**: 根据查询复杂度自动确定并行调用的层次和数量
-   **缓存策略**: 相同区域的基础信息(天气、POI)采用缓存减少重复调用
-   **错误降级**: 若某个工具调用失败，自动采用备选方案继续执行
-   **结果质量控制**: 实时评估API返回的数据质量，过滤低质量POI
-   **用户反馈学习**: 根据用户对推荐结果的反馈，动态调整工具调用的权重和策略
