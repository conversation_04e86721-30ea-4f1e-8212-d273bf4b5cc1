# 项目任务清单

## 阶段一: 项目初始化与基础架构

- [x] 初始化 Memory Bank (创建核心文件: projectbrief.md, tasks.md 等)
- [x] **已完成:** 搭建项目基础结构 (目录、HTML骨架、CSS、JS入口)
- [x] **已完成:** 完善 `projectbrief.md` (已根据完整文档内容更新)
- [x] **已完成:** 详细规划 `productContext.md` (已填充主要内容)
- [x] **已完成:** 详细规划 `systemPatterns.md` (已填充主要内容)
- [x] **已完成:** 详细规划 `techContext.md` (已填充主要内容并包含AI服务信息)
- [x] **已完成:** 详细规划 `style-guide.md` (已填充主要内容)

## 阶段二: 游戏门户容器开发

- [ ] **当前任务:** 实现门户UI (游戏选择菜单、游戏展示区)
- [x] **已完成:** 实现门户UI (游戏选择菜单CSS细化、iframe区域CSS细化，JS激活状态处理)
- [ ] **当前任务:** 实现游戏加载逻辑 (通过 `<iframe>`)
- [x] **已完成:** 实现游戏加载逻辑 (通过 `<iframe>`，已创建占位符游戏页面并确认可加载)

## 阶段三: 示例游戏开发 - 吃豆人 (Pac-Man)

### 当前焦点: 实现吃豆人核心逻辑

**概览:** 从头开始逐步构建吃豆人游戏，包括玩家控制、迷宫、豆子、幽灵和得分机制。

**主要子任务:**

1.  **[进行中] 基础框架与玩家控制 (当前)**
    *   [x] 创建 Pac-Man HTML (`games/pacman/index.html`)
    *   [x] 创建 Pac-Man CSS (`games/pacman/css/pacman.css`)
    *   [x] 创建 Pac-Man JS (`games/pacman/js/pacman.js`)
    *   [x] 初始化 Canvas 和 2D 上下文
    *   [x] 实现 Pac-Man 对象/类 (位置, 速度, 颜色)
    *   [x] 实现 Pac-Man 绘制逻辑 (带嘴巴动画)
    *   [x] 实现键盘输入控制 Pac-Man 方向
    *   [x] 实现基础游戏循环 (`requestAnimationFrame`)
    *   [ ] **下一步:** 定义迷宫布局数据结构

2.  **迷宫实现**
    *   [ ] 设计并实现迷宫数据结构 (例如，二维数组表示墙壁和路径)
    *   [ ] 绘制迷宫到 Canvas
    *   [ ] 实现 Pac-Man 与墙壁的碰撞检测
    *   [ ] 确保 Pac-Man 只能在路径上移动

3.  **豆子 (Pellets) 和能量豆 (Power Pellets)**
    *   [ ] 定义豆子和能量豆的数据结构和属性
    *   [ ] 在迷宫路径上散布豆子和能量豆
    *   [ ] 实现 Pac-Man 吃豆子的逻辑
    *   [ ] 实现吃豆子得分功能
    *   [ ] 实现吃能量豆后的特殊效果 (例如，幽灵暂时变弱)

4.  **幽灵 (Ghosts) 实现**
    *   [ ] 创建幽灵对象/类 (不同颜色，不同行为模式)
    *   [ ] 实现幽灵的绘制逻辑
    *   [ ] 实现基础的幽灵移动逻辑 (例如，随机移动或简单追逐)
    *   [ ] 实现幽灵与墙壁的碰撞检测
    *   [ ] **(AI 驱动)** 设计和集成 AI 算法驱动幽灵行为 (例如，Blinky 的追逐, Pinky 的预判, Inky 的包抄, Clyde 的巡逻/随机)

5.  **游戏状态与逻辑**
    *   [ ] 实现生命系统 (被幽灵抓住则减少生命)
    *   [ ] 实现游戏结束条件 (生命耗尽)
    *   [ ] 实现关卡完成条件 (所有豆子被吃完)
    *   [ ] 实现游戏重置/重新开始逻辑
    *   [ ] 实现分数显示和更新
    *   [ ] 实现幽灵在被吃掉能量豆后变弱及可被吃掉的状态
    *   [ ] 实现幽灵被吃掉后的行为 (例如，暂时回到基地)

6.  **音效与视觉细节 (可选)**
    *   [ ] 添加吃豆子音效
    *   [ ] 添加 Pac-Man 移动音效
    *   [ ] 添加游戏开始/结束音效
    *   [ ] 优化视觉效果和动画

7.  **AI 内容生成 (迷宫)**
    *   [ ] **(AI 驱动)** 研究并集成使用 Zhipu AI (BigModel) API (glm-z1-flash) 动态生成迷宫布局的逻辑。
    *   [ ] 设计 prompt 工程以获得有效的迷宫数据。
    *   [ ] 实现从 AI API 获取数据并解析为游戏内迷宫格式的功能。
    *   [ ] 添加错误处理和备用迷宫方案 (如果 AI 服务失败)。

**后续游戏 (待定):**
*   俄罗斯方块 (Tetris)
*   坦克大战 (Battle City)
*   贪吃蛇 (Snake)

## 后续阶段

- [ ] 集成更多游戏
- [ ] 详细测试与QA
- [ ] 部署 

# 吃豆人游戏开发任务列表

## 阶段一：基础框架与核心机制 (已大部分完成)

*   [x] **项目初始化**
    *   [x] 创建 HTML 结构 (`index.html`)
    *   [x] 创建 CSS 样式 (`pacman.css`)
    *   [x] 创建 JavaScript 主文件 (`pacman.js`)
*   [x] **游戏板与迷宫**
    *   [x] 定义游戏板参数 (TILE_SIZE, BOARD_COLS, BOARD_ROWS)
    *   [x] 设计迷宫数据结构 (`map` 二维数组)
        *   [x] 包含墙壁 (1), 路径 (0), 能量豆 (2), 幽灵门 (3), 幽灵之家 (4), 吃豆人起点 (5), 隧道 (6), 普通豆子 (7)
    *   [x] 实现 `isObstacle` 碰撞检测辅助函数
    *   [x] 绘制迷宫 (`drawWall`, `drawMap`)
*   [x] **吃豆人 (Pac-Man)**
    *   [x] 创建 `pacman` 对象 (位置, 速度, 颜色, 绘制)
    *   [x] 实现吃豆人动画 (嘴巴开合)
    *   [x] 实现键盘输入控制
    *   [x] **优化转弯逻辑** (输入缓冲 `desiredDirection`, 格子对齐判断，平滑转向)
    *   [x] 实现与墙壁的碰撞检测
    *   [x] 实现隧道穿行逻辑
    *   [x] 调整吃豆人移动速度
*   [x] **幽灵 (Ghosts)**
    *   [x] 创建 `Ghost` 类 (位置, 速度, 颜色, 绘制)
    *   [x] 实现幽灵动画 (身体和眼睛)
    *   [x] 实现基础随机移动逻辑 (`chooseNewDirection`)
    *   [x] 实现幽灵与墙壁的碰撞检测
    *   [x] 实现幽灵隧道穿行逻辑
    *   [x] 初始化多个幽灵实例
    *   [x] 调整幽灵移动速度
*   [x] **豆子 (Pellets) 和能量豆 (Power Pellets)**
    *   [x] 在地图上布置豆子和能量豆
    *   [x] 实现豆子和能量豆的绘制 (`drawPellet`, `drawPowerPellet`)
    *   [x] 实现吃豆人吃豆子/能量豆的逻辑
    *   [x] 实现吃豆子/能量豆得分功能
*   [x] **得分与生命系统**
    *   [x] 初始化并显示得分和生命值
    *   [x] 更新得分显示

## 阶段二：核心游戏逻辑增强

*   [ ] **能量豆特殊效果**
    *   [ ] 幽灵进入 "受惊" (FRIGHTENED) 状态
        *   [ ] 改变幽灵颜色 (例如，变为蓝色)
        *   [ ] 幽灵移动速度可能减慢 (可选)
        *   [ ] 幽灵行为模式改变 (例如，随机逃跑)
    *   [ ] 吃豆人可以吃掉 "受惊" 状态的幽灵
        *   [ ] 幽灵被吃掉后返回幽灵之家
        *   [ ] 获得额外分数
    *   [ ] 实现 "受惊" 状态的计时器
*   [ ] **过关逻辑**
    *   [ ] 检测所有普通豆子是否被吃完
    *   [ ] 如果所有豆子吃完，进入下一关 (或提示游戏胜利)
        *   [ ] 重置豆子和能量豆
        *   [ ] 重置吃豆人和幽灵位置
        *   [ ] (可选) 增加幽灵难度/速度
*   [ ] **游戏结束逻辑**
    *   [ ] 吃豆人被幽灵抓住 (非受惊状态)
        *   [ ] 减少生命值
        *   [ ] 播放死亡动画 (可选)
        *   [ ] 重置吃豆人和幽灵位置 (如果还有生命)
    *   [ ] 生命值耗尽，游戏结束
        *   [ ] 显示 "Game Over"
        *   [ ] 提供重新开始选项

## 阶段三：AI 与细节完善

*   [ ] **幽灵AI行为增强**
    *   [ ] 实现不同幽灵的独特追逐模式 (Blinky, Pinky, Inky, Clyde)
    *   [ ] 实现幽灵的 "巡逻" (SCATTER) 模式
    *   [ ] 实现模式切换逻辑 (例如，基于计时器或吃豆数量)
*   [ ] **地图与豆子布局优化**
    *   [ ] 根据经典 Pac-Man 地图或更优化的布局调整 `map` 数组
    *   [ ] 确保豆子和能量豆的摆放合理且具有挑战性
*   [ ] **音效 (可选)**
    *   [ ] 吃豆子音效
    *   [ ] 吃能量豆音效
    *   [ ] 吃豆人死亡音效
    *   [ ] 游戏开始/背景音乐
*   [ ] **UI/UX 优化 (可选)**
    *   [ ] 更好的游戏开始/结束界面
    *   [ ] 关卡指示

## 阶段四：代码整理与测试
*   [ ] 代码重构和优化
*   [ ] 完整测试和 Bug 修复 