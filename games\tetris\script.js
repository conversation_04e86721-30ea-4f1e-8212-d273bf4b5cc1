// 游戏常量
const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 20;
const BLOCK_SIZE = 30;

// DOM元素
const startScreen = document.getElementById('startScreen');
const pauseScreen = document.getElementById('pauseScreen');
const gameOverScreen = document.getElementById('gameOverScreen');
const gameArea = document.getElementById('gameArea');
const gameCanvas = document.getElementById('gameCanvas');
const nextCanvas = document.getElementById('nextCanvas');
const holdCanvas = document.getElementById('holdCanvas');
const ctx = gameCanvas.getContext('2d');
const nextCtx = nextCanvas.getContext('2d');
const holdCtx = holdCanvas.getContext('2d');

// UI元素
const startBtn = document.getElementById('startBtn');
const pauseBtn = document.getElementById('pauseBtn');
const resumeBtn = document.getElementById('resumeBtn');
const restartBtn = document.getElementById('restartBtn');
const restartGameBtn = document.getElementById('restartGameBtn');
const playAgainBtn = document.getElementById('playAgainBtn');

// 分数和统计元素
const scoreElement = document.getElementById('score');
const levelElement = document.getElementById('level');
const linesElement = document.getElementById('lines');
const comboElement = document.getElementById('combo');
const progressFill = document.getElementById('progressFill');
const progressLines = document.getElementById('progressLines');
const progressTarget = document.getElementById('progressTarget');
const finalScore = document.getElementById('finalScore');
const finalLevel = document.getElementById('finalLevel');
const particleContainer = document.getElementById('particleContainer');

// 游戏状态
let gameState = 'menu'; // 'menu', 'playing', 'paused', 'gameOver'
let board = [];
let currentPiece = null;
let nextPiece = null;
let holdPiece = null;
let canHold = true;
let score = 0;
let level = 1;
let lines = 0;
let combo = 0;
let dropTime = 0;
let lastTime = 0;

// 俄罗斯方块形状定义
const TETROMINOES = {
    I: {
        shape: [
            [0, 0, 0, 0],
            [1, 1, 1, 1],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ],
        color: '#00f0f0'
    },
    J: {
        shape: [
            [1, 0, 0],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#0000f0'
    },
    L: {
        shape: [
            [0, 0, 1],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#f0a000'
    },
    O: {
        shape: [
            [1, 1],
            [1, 1]
        ],
        color: '#f0f000'
    },
    S: {
        shape: [
            [0, 1, 1],
            [1, 1, 0],
            [0, 0, 0]
        ],
        color: '#00f000'
    },
    T: {
        shape: [
            [0, 1, 0],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#a000f0'
    },
    Z: {
        shape: [
            [1, 1, 0],
            [0, 1, 1],
            [0, 0, 0]
        ],
        color: '#f00000'
    }
};

// 初始化游戏板
function createBoard() {
    return Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
}

// 创建新的俄罗斯方块
function createPiece(type) {
    const tetromino = TETROMINOES[type];
    return {
        shape: tetromino.shape,
        color: tetromino.color,
        x: Math.floor(BOARD_WIDTH / 2) - Math.floor(tetromino.shape[0].length / 2),
        y: 0,
        type: type
    };
}

// 获取随机方块类型
function getRandomPieceType() {
    const types = Object.keys(TETROMINOES);
    return types[Math.floor(Math.random() * types.length)];
}

// 旋转矩阵
function rotate(matrix) {
    const N = matrix.length;
    const rotated = matrix.map((row, i) =>
        row.map((val, j) => matrix[N - 1 - j][i])
    );
    return rotated;
}

// 检查碰撞
function isValidMove(piece, dx = 0, dy = 0, rotatedShape = null) {
    const shape = rotatedShape || piece.shape;
    const newX = piece.x + dx;
    const newY = piece.y + dy;
    
    for (let y = 0; y < shape.length; y++) {
        for (let x = 0; x < shape[y].length; x++) {
            if (shape[y][x]) {
                const boardX = newX + x;
                const boardY = newY + y;
                
                if (boardX < 0 || boardX >= BOARD_WIDTH || 
                    boardY >= BOARD_HEIGHT || 
                    (boardY >= 0 && board[boardY][boardX])) {
                    return false;
                }
            }
        }
    }
    return true;
}

// 移动方块
function movePiece(dx, dy) {
    if (isValidMove(currentPiece, dx, dy)) {
        currentPiece.x += dx;
        currentPiece.y += dy;
        return true;
    }
    return false;
}

// 旋转方块
function rotatePiece() {
    const rotated = rotate(currentPiece.shape);
    if (isValidMove(currentPiece, 0, 0, rotated)) {
        currentPiece.shape = rotated;
        createRotateEffect(currentPiece.x + currentPiece.shape[0].length / 2, currentPiece.y + currentPiece.shape.length / 2);
    }
}

// 放置方块到游戏板
function placePiece() {
    for (let y = 0; y < currentPiece.shape.length; y++) {
        for (let x = 0; x < currentPiece.shape[y].length; x++) {
            if (currentPiece.shape[y][x]) {
                const boardX = currentPiece.x + x;
                const boardY = currentPiece.y + y;
                if (boardY >= 0) {
                    board[boardY][boardX] = currentPiece.color;
                }
            }
        }
    }
}

// 检查并清除完整行
function clearLines() {
    let linesCleared = 0;
    const clearedRows = [];
    
    for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
        if (board[y].every(cell => cell !== 0)) {
            clearedRows.push(y);
            board.splice(y, 1);
            board.unshift(Array(BOARD_WIDTH).fill(0));
            linesCleared++;
            y++; // 重新检查这一行
        }
    }
    
    if (linesCleared > 0) {
        // 创建消除特效
        clearedRows.forEach(row => {
            createLineClearEffect(row);
        });
        
        // 更新分数和统计
        lines += linesCleared;
        combo += linesCleared;
        
        // 计算分数 (使用经典俄罗斯方块计分系统)
        const baseScore = [0, 40, 100, 300, 1200][linesCleared];
        const comboBonus = Math.max(0, combo - 4) * 50;
        score += (baseScore + comboBonus) * level;
        
        // 等级提升
        const newLevel = Math.floor(lines / 10) + 1;
        if (newLevel > level) {
            level = newLevel;
            createLevelUpEffect();
        }
        
        updateUI();
        createScoreEffect(linesCleared);
    } else {
        combo = 0;
        updateUI();
    }
}

// 生成下一个方块
function spawnNextPiece() {
    if (!nextPiece) {
        nextPiece = createPiece(getRandomPieceType());
    }
    
    currentPiece = nextPiece;
    nextPiece = createPiece(getRandomPieceType());
    canHold = true;
    
    // 检查游戏结束
    if (!isValidMove(currentPiece, 0, 0)) {
        gameOver();
    }
}

// 暂存方块功能
function holdCurrentPiece() {
    if (!canHold) return;
    
    if (holdPiece) {
        // 交换当前方块和暂存方块
        const temp = holdPiece;
        holdPiece = createPiece(currentPiece.type);
        currentPiece = createPiece(temp.type);
        currentPiece.x = Math.floor(BOARD_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
        currentPiece.y = 0;
    } else {
        // 将当前方块放入暂存
        holdPiece = createPiece(currentPiece.type);
        spawnNextPiece();
    }
    
    canHold = false;
}

// 硬降
function hardDrop() {
    while (movePiece(0, 1)) {}
    placePiece();
    clearLines();
    spawnNextPiece();
}

// 软降
function softDrop() {
    if (movePiece(0, 1)) {
        score += 1;
        updateUI();
    }
}

// 更新UI
function updateUI() {
    scoreElement.textContent = score.toLocaleString();
    levelElement.textContent = level;
    linesElement.textContent = lines;
    comboElement.textContent = combo;
    
    // 更新进度条
    const currentLevelLines = lines % 10;
    const targetLines = 10;
    const progressPercent = (currentLevelLines / targetLines) * 100;
    progressFill.style.width = progressPercent + '%';
    progressLines.textContent = currentLevelLines;
    progressTarget.textContent = targetLines;
}

// 绘制单个方块
function drawBlock(ctx, x, y, color, size = BLOCK_SIZE) {
    const gradient = ctx.createLinearGradient(x, y, x + size, y + size);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, darkenColor(color, 0.3));
    
    ctx.fillStyle = gradient;
    ctx.fillRect(x, y, size - 1, size - 1);
    
    // 添加高光效果
    ctx.fillStyle = lightenColor(color, 0.4);
    ctx.fillRect(x + 2, y + 2, size - 8, 3);
    ctx.fillRect(x + 2, y + 2, 3, size - 8);
}

// 颜色处理函数
function darkenColor(color, factor) {
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);
    
    const newR = Math.floor(r * (1 - factor));
    const newG = Math.floor(g * (1 - factor));
    const newB = Math.floor(b * (1 - factor));
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

function lightenColor(color, factor) {
    const r = parseInt(color.slice(1, 3), 16);
    const g = parseInt(color.slice(3, 5), 16);
    const b = parseInt(color.slice(5, 7), 16);
    
    const newR = Math.floor(r + (255 - r) * factor);
    const newG = Math.floor(g + (255 - g) * factor);
    const newB = Math.floor(b + (255 - b) * factor);
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

// 绘制方块形状
function drawPiece(ctx, piece, offsetX = 0, offsetY = 0, size = BLOCK_SIZE) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                drawBlock(
                    ctx,
                    (piece.x + x) * size + offsetX,
                    (piece.y + y) * size + offsetY,
                    piece.color,
                    size
                );
            }
        }
    }
}

// 绘制预览方块
function drawPreviewPiece(ctx, piece, canvas) {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (piece) {
        const centerX = (canvas.width - piece.shape[0].length * 20) / 2;
        const centerY = (canvas.height - piece.shape.length * 20) / 2;
        
        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    drawBlock(ctx, centerX + x * 20, centerY + y * 20, piece.color, 20);
                }
            }
        }
    }
}

// 绘制游戏板
function drawBoard() {
    ctx.clearRect(0, 0, gameCanvas.width, gameCanvas.height);
    
    // 绘制网格
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    for (let x = 0; x <= BOARD_WIDTH; x++) {
        ctx.beginPath();
        ctx.moveTo(x * BLOCK_SIZE, 0);
        ctx.lineTo(x * BLOCK_SIZE, BOARD_HEIGHT * BLOCK_SIZE);
        ctx.stroke();
    }
    
    for (let y = 0; y <= BOARD_HEIGHT; y++) {
        ctx.beginPath();
        ctx.moveTo(0, y * BLOCK_SIZE);
        ctx.lineTo(BOARD_WIDTH * BLOCK_SIZE, y * BLOCK_SIZE);
        ctx.stroke();
    }
    
    // 绘制已放置的方块
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (board[y][x]) {
                drawBlock(ctx, x * BLOCK_SIZE, y * BLOCK_SIZE, board[y][x]);
            }
        }
    }
    
    // 绘制当前方块
    if (currentPiece) {
        drawPiece(ctx, currentPiece);
        
        // 绘制影子方块
        const shadowPiece = { ...currentPiece };
        while (isValidMove(shadowPiece, 0, 1)) {
            shadowPiece.y++;
        }
        
        ctx.globalAlpha = 0.3;
        drawPiece(ctx, shadowPiece);
        ctx.globalAlpha = 1.0;
    }
}

// 特效系统
function createParticle(x, y, color = '#ffd93d') {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.left = x + 'px';
    particle.style.top = y + 'px';
    particle.style.background = color;
    
    const angle = Math.random() * Math.PI * 2;
    const velocity = 20 + Math.random() * 30;
    particle.style.setProperty('--vx', Math.cos(angle) * velocity + 'px');
    particle.style.setProperty('--vy', Math.sin(angle) * velocity + 'px');
    
    particleContainer.appendChild(particle);
    
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 2000);
}

function createLineClearEffect(row) {
    const effect = document.createElement('div');
    effect.className = 'line-clear-effect';
    effect.style.left = '0';
    effect.style.top = (row * BLOCK_SIZE) + 'px';
    effect.style.width = (BOARD_WIDTH * BLOCK_SIZE) + 'px';
    
    gameCanvas.parentNode.appendChild(effect);
    
    // 创建粒子效果
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            const x = Math.random() * BOARD_WIDTH * BLOCK_SIZE;
            const y = row * BLOCK_SIZE + 15;
            createParticle(x, y, '#4ecdc4');
        }, i * 30);
    }
    
    setTimeout(() => {
        if (effect.parentNode) {
            effect.parentNode.removeChild(effect);
        }
    }, 600);
}

function createRotateEffect(x, y) {
    for (let i = 0; i < 8; i++) {
        setTimeout(() => {
            createParticle(
                x * BLOCK_SIZE + Math.random() * 40 - 20,
                y * BLOCK_SIZE + Math.random() * 40 - 20,
                '#667eea'
            );
        }, i * 50);
    }
}

function createScoreEffect(linesCleared) {
    const effects = ['单行!', '双行!!', '三行!!!', '四行!!!!'];
    const effect = effects[linesCleared - 1] || '消除!';
    
    const popup = document.createElement('div');
    popup.style.position = 'fixed';
    popup.style.left = '50%';
    popup.style.top = '30%';
    popup.style.transform = 'translate(-50%, -50%)';
    popup.style.fontSize = '2em';
    popup.style.fontWeight = 'bold';
    popup.style.color = '#ffd93d';
    popup.style.textShadow = '0 0 10px rgba(255, 217, 61, 0.8)';
    popup.style.pointerEvents = 'none';
    popup.style.zIndex = '1002';
    popup.style.animation = 'scorePopup 1.5s ease-out forwards';
    popup.textContent = effect;
    
    document.body.appendChild(popup);
    
    setTimeout(() => {
        if (popup.parentNode) {
            popup.parentNode.removeChild(popup);
        }
    }, 1500);
}

function createLevelUpEffect() {
    const popup = document.createElement('div');
    popup.style.position = 'fixed';
    popup.style.left = '50%';
    popup.style.top = '40%';
    popup.style.transform = 'translate(-50%, -50%)';
    popup.style.fontSize = '2.5em';
    popup.style.fontWeight = 'bold';
    popup.style.color = '#4ecdc4';
    popup.style.textShadow = '0 0 20px rgba(78, 205, 196, 0.8)';
    popup.style.pointerEvents = 'none';
    popup.style.zIndex = '1002';
    popup.style.animation = 'levelUp 2s ease-out forwards';
    popup.textContent = `等级 ${level}!`;
    
    document.body.appendChild(popup);
    
    // 创建大量粒子效果
    for (let i = 0; i < 50; i++) {
        setTimeout(() => {
            createParticle(
                window.innerWidth / 2 + Math.random() * 200 - 100,
                window.innerHeight / 2 + Math.random() * 200 - 100,
                '#4ecdc4'
            );
        }, i * 20);
    }
    
    setTimeout(() => {
        if (popup.parentNode) {
            popup.parentNode.removeChild(popup);
        }
    }, 2000);
}

// 游戏控制
function startGame() {
    gameState = 'playing';
    board = createBoard();
    score = 0;
    level = 1;
    lines = 0;
    combo = 0;
    dropTime = 0;
    
    nextPiece = createPiece(getRandomPieceType());
    spawnNextPiece();
    
    startScreen.style.display = 'none';
    gameArea.style.display = 'flex';
    
    updateUI();
    gameLoop();
}

function pauseGame() {
    if (gameState === 'playing') {
        gameState = 'paused';
        pauseScreen.style.display = 'flex';
    }
}

function resumeGame() {
    if (gameState === 'paused') {
        gameState = 'playing';
        pauseScreen.style.display = 'none';
        gameLoop();
    }
}

function restartGame() {
    gameState = 'menu';
    pauseScreen.style.display = 'none';
    gameOverScreen.style.display = 'none';
    gameArea.style.display = 'none';
    startScreen.style.display = 'flex';
}

function gameOver() {
    gameState = 'gameOver';
    finalScore.textContent = score.toLocaleString();
    finalLevel.textContent = level;
    gameOverScreen.style.display = 'flex';
}

// 游戏循环
function gameLoop(time = 0) {
    if (gameState !== 'playing') return;
    
    const deltaTime = time - lastTime;
    lastTime = time;
    
    dropTime += deltaTime;
    const dropInterval = Math.max(50, 1000 - (level - 1) * 50);
    
    if (dropTime > dropInterval) {
        if (!movePiece(0, 1)) {
            placePiece();
            clearLines();
            spawnNextPiece();
        }
        dropTime = 0;
    }
    
    // 渲染
    drawBoard();
    drawPreviewPiece(nextCtx, nextPiece, nextCanvas);
    drawPreviewPiece(holdCtx, holdPiece, holdCanvas);
    
    requestAnimationFrame(gameLoop);
}

// 键盘控制
document.addEventListener('keydown', (e) => {
    if (gameState !== 'playing') {
        if (e.code === 'Space' && gameState === 'paused') {
            resumeGame();
        }
        return;
    }
    
    switch (e.code) {
        case 'ArrowLeft':
            movePiece(-1, 0);
            break;
        case 'ArrowRight':
            movePiece(1, 0);
            break;
        case 'ArrowDown':
            softDrop();
            break;
        case 'ArrowUp':
            rotatePiece();
            break;
        case 'Space':
            pauseGame();
            break;
        case 'KeyC':
            holdCurrentPiece();
            break;
        case 'KeyX':
            hardDrop();
            break;
    }
    
    e.preventDefault();
});

// 添加动画CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes scorePopup {
        0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
        50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
        100% { transform: translate(-50%, -50%) scale(1) translateY(-50px); opacity: 0; }
    }
    
    @keyframes levelUp {
        0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
        20% { transform: translate(-50%, -50%) scale(1.3); opacity: 1; }
        80% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
        100% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 事件监听器
startBtn.addEventListener('click', startGame);
pauseBtn.addEventListener('click', pauseGame);
resumeBtn.addEventListener('click', resumeGame);
restartBtn.addEventListener('click', restartGame);
restartGameBtn.addEventListener('click', restartGame);
playAgainBtn.addEventListener('click', startGame);

// 初始化
updateUI(); 