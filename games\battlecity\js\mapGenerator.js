// 地图生成系统
class MapGenerator {
    constructor() {
        this.mapWidth = 26; // 26x26 网格
        this.mapHeight = 26;
        this.tileSize = 32;
        this.currentLevel = 1;
        
        // 预定义的经典关卡模板
        this.classicMaps = [
            // 关卡1 - 简单布局
            [
                "##########################",
                "#                        #",
                "#  ##    ##    ##    ##  #",
                "#  ##    ##    ##    ##  #",
                "#                        #",
                "#    ####        ####    #",
                "#    ####        ####    #",
                "#                        #",
                "#  ####################  #",
                "#  ####################  #",
                "#                        #",
                "#    ####        ####    #",
                "#    ####        ####    #",
                "#                        #",
                "#  ##    ##    ##    ##  #",
                "#  ##    ##    ##    ##  #",
                "#                        #",
                "#    ####        ####    #",
                "#    ####        ####    #",
                "#                        #",
                "#  ####################  #",
                "#  ####################  #",
                "#                        #",
                "#           BB           #",
                "#           BB           #",
                "##########################"
            ],
            // 关卡2 - 迷宫布局
            [
                "##########################",
                "#                        #",
                "# ## #### ## #### ## ### #",
                "# ## #### ## #### ## ### #",
                "#    ####    ####        #",
                "#### #### ## #### ###### #",
                "#### #### ## #### ###### #",
                "#         ##             #",
                "# ###### #### ###### ### #",
                "# ###### #### ###### ### #",
                "#        ####        ### #",
                "######## #### ########## #",
                "######## #### ########## #",
                "#        ####            #",
                "# ###### #### ###### ### #",
                "# ###### #### ###### ### #",
                "#         ##         ### #",
                "#### #### ## #### ###### #",
                "#### #### ## #### ###### #",
                "#    ####    ####    ### #",
                "# ## #### ## #### ## ### #",
                "# ## #### ## #### ## ### #",
                "#                        #",
                "#           BB           #",
                "#           BB           #",
                "##########################"
            ]
        ];
        
        // 地形类型映射
        this.terrainMap = {
            '#': 'brick',    // 砖墙
            'S': 'steel',    // 钢墙
            'W': 'water',    // 水
            'G': 'grass',    // 草地
            'B': 'base',     // 基地
            ' ': 'empty'     // 空地
        };
    }

    // 生成指定关卡的地图
    generateLevel(level) {
        this.currentLevel = level;
        
        if (level <= this.classicMaps.length) {
            // 使用预定义地图
            return this.parseClassicMap(this.classicMaps[level - 1]);
        } else {
            // 生成随机地图
            return this.generateRandomMap(level);
        }
    }

    // 解析经典地图
    parseClassicMap(mapData) {
        const walls = [];
        let base = null;
        
        for (let y = 0; y < mapData.length; y++) {
            const row = mapData[y];
            for (let x = 0; x < row.length; x++) {
                const char = row[x];
                const pixelX = x * this.tileSize;
                const pixelY = y * this.tileSize;
                
                if (char === 'B') {
                    // 基地位置（2x2）
                    if (!base) {
                        base = { x: pixelX, y: pixelY };
                    }
                } else if (char !== ' ') {
                    // 墙壁
                    const terrainType = this.terrainMap[char] || 'brick';
                    walls.push({
                        x: pixelX,
                        y: pixelY,
                        type: terrainType
                    });
                }
            }
        }
        
        return {
            walls,
            base: base || { x: 384, y: 736 }, // 默认基地位置
            playerSpawn: { x: 96, y: 736 },   // 玩家出生点
            enemySpawns: [                     // 敌人出生点
                { x: 32, y: 32 },
                { x: 384, y: 32 },
                { x: 736, y: 32 }
            ]
        };
    }

    // 生成随机地图
    generateRandomMap(level) {
        const walls = [];
        const difficulty = Math.min(level / 5, 1); // 难度系数 0-1
        
        // 创建边界墙
        this.createBorderWalls(walls);
        
        // 生成随机障碍物
        this.generateRandomObstacles(walls, difficulty);
        
        // 生成水域
        this.generateWaterAreas(walls, difficulty);
        
        // 生成钢墙
        this.generateSteelWalls(walls, difficulty);
        
        // 生成草地
        this.generateGrassAreas(walls);
        
        // 确保路径通畅
        this.ensurePathways(walls);
        
        return {
            walls,
            base: { x: 384, y: 736 },
            playerSpawn: { x: 96, y: 736 },
            enemySpawns: [
                { x: 32, y: 32 },
                { x: 384, y: 32 },
                { x: 736, y: 32 }
            ]
        };
    }

    // 创建边界墙
    createBorderWalls(walls) {
        // 顶部和底部边界
        for (let x = 0; x < this.mapWidth; x++) {
            walls.push({
                x: x * this.tileSize,
                y: 0,
                type: 'steel'
            });
            walls.push({
                x: x * this.tileSize,
                y: (this.mapHeight - 1) * this.tileSize,
                type: 'steel'
            });
        }
        
        // 左右边界
        for (let y = 1; y < this.mapHeight - 1; y++) {
            walls.push({
                x: 0,
                y: y * this.tileSize,
                type: 'steel'
            });
            walls.push({
                x: (this.mapWidth - 1) * this.tileSize,
                y: y * this.tileSize,
                type: 'steel'
            });
        }
    }

    // 生成随机障碍物
    generateRandomObstacles(walls, difficulty) {
        const obstacleCount = Math.floor(50 + difficulty * 30);
        
        for (let i = 0; i < obstacleCount; i++) {
            const x = Math.floor(Math.random() * (this.mapWidth - 4)) + 2;
            const y = Math.floor(Math.random() * (this.mapHeight - 6)) + 2;
            
            // 避免在重要区域生成
            if (this.isImportantArea(x, y)) continue;
            
            // 生成2x2的障碍物块
            for (let dx = 0; dx < 2; dx++) {
                for (let dy = 0; dy < 2; dy++) {
                    if (Math.random() < 0.8) { // 80%概率生成
                        walls.push({
                            x: (x + dx) * this.tileSize,
                            y: (y + dy) * this.tileSize,
                            type: 'brick'
                        });
                    }
                }
            }
        }
    }

    // 生成水域
    generateWaterAreas(walls, difficulty) {
        const waterCount = Math.floor(3 + difficulty * 2);
        
        for (let i = 0; i < waterCount; i++) {
            const x = Math.floor(Math.random() * (this.mapWidth - 6)) + 3;
            const y = Math.floor(Math.random() * (this.mapHeight - 8)) + 3;
            
            if (this.isImportantArea(x, y)) continue;
            
            // 生成不规则水域
            const size = 2 + Math.floor(Math.random() * 3);
            for (let dx = 0; dx < size; dx++) {
                for (let dy = 0; dy < size; dy++) {
                    if (Math.random() < 0.7) {
                        walls.push({
                            x: (x + dx) * this.tileSize,
                            y: (y + dy) * this.tileSize,
                            type: 'water'
                        });
                    }
                }
            }
        }
    }

    // 生成钢墙
    generateSteelWalls(walls, difficulty) {
        const steelCount = Math.floor(5 + difficulty * 10);
        
        for (let i = 0; i < steelCount; i++) {
            const x = Math.floor(Math.random() * (this.mapWidth - 4)) + 2;
            const y = Math.floor(Math.random() * (this.mapHeight - 6)) + 2;
            
            if (this.isImportantArea(x, y)) continue;
            
            walls.push({
                x: x * this.tileSize,
                y: y * this.tileSize,
                type: 'steel'
            });
        }
    }

    // 生成草地
    generateGrassAreas(walls) {
        const grassCount = Math.floor(8 + Math.random() * 5);
        
        for (let i = 0; i < grassCount; i++) {
            const x = Math.floor(Math.random() * (this.mapWidth - 4)) + 2;
            const y = Math.floor(Math.random() * (this.mapHeight - 6)) + 2;
            
            // 生成草地区域
            const size = 1 + Math.floor(Math.random() * 2);
            for (let dx = 0; dx < size; dx++) {
                for (let dy = 0; dy < size; dy++) {
                    walls.push({
                        x: (x + dx) * this.tileSize,
                        y: (y + dy) * this.tileSize,
                        type: 'grass'
                    });
                }
            }
        }
    }

    // 检查是否为重要区域（玩家出生点、基地、敌人出生点附近）
    isImportantArea(x, y) {
        // 玩家出生点附近 (3, 23)
        if (Math.abs(x - 3) < 3 && Math.abs(y - 23) < 3) return true;
        
        // 基地附近 (12, 23)
        if (Math.abs(x - 12) < 4 && Math.abs(y - 23) < 4) return true;
        
        // 敌人出生点附近
        const enemySpawns = [[1, 1], [12, 1], [23, 1]];
        for (const [ex, ey] of enemySpawns) {
            if (Math.abs(x - ex) < 3 && Math.abs(y - ey) < 3) return true;
        }
        
        return false;
    }

    // 确保路径通畅
    ensurePathways(walls) {
        // 创建主要通道
        this.createMainPathways(walls);
        
        // 确保基地可达
        this.ensureBaseAccess(walls);
    }

    // 创建主要通道
    createMainPathways(walls) {
        // 水平通道
        for (let y = 6; y < this.mapHeight - 6; y += 6) {
            for (let x = 2; x < this.mapWidth - 2; x++) {
                this.removeWallAt(walls, x * this.tileSize, y * this.tileSize);
            }
        }
        
        // 垂直通道
        for (let x = 6; x < this.mapWidth - 6; x += 6) {
            for (let y = 2; y < this.mapHeight - 6; y++) {
                this.removeWallAt(walls, x * this.tileSize, y * this.tileSize);
            }
        }
    }

    // 确保基地可达
    ensureBaseAccess(walls) {
        const baseX = 12;
        const baseY = 23;
        
        // 清理基地周围的路径
        for (let dx = -2; dx <= 3; dx++) {
            for (let dy = -3; dy <= 1; dy++) {
                const x = (baseX + dx) * this.tileSize;
                const y = (baseY + dy) * this.tileSize;
                this.removeWallAt(walls, x, y);
            }
        }
    }

    // 移除指定位置的墙壁
    removeWallAt(walls, x, y) {
        for (let i = walls.length - 1; i >= 0; i--) {
            const wall = walls[i];
            if (wall.x === x && wall.y === y && wall.type !== 'steel') {
                walls.splice(i, 1);
            }
        }
    }

    // 生成敌人配置
    generateEnemyConfig(level) {
        const baseEnemyCount = 4;
        const additionalEnemies = Math.floor(level / 2);
        const totalEnemies = Math.min(baseEnemyCount + additionalEnemies, 20);
        
        // 敌人类型配置
        const enemyTypes = [
            { type: 'basic', health: 1, speed: 1, count: 0 },
            { type: 'fast', health: 1, speed: 2, count: 0 },
            { type: 'armor', health: 2, speed: 1, count: 0 },
            { type: 'heavy', health: 3, speed: 0.5, count: 0 }
        ];
        
        // 根据关卡分配敌人类型
        let remainingEnemies = totalEnemies;
        
        // 基础敌人（总是有一些）
        enemyTypes[0].count = Math.floor(totalEnemies * 0.4);
        remainingEnemies -= enemyTypes[0].count;
        
        // 快速敌人（从关卡3开始）
        if (level >= 3) {
            enemyTypes[1].count = Math.floor(remainingEnemies * 0.3);
            remainingEnemies -= enemyTypes[1].count;
        }
        
        // 装甲敌人（从关卡5开始）
        if (level >= 5) {
            enemyTypes[2].count = Math.floor(remainingEnemies * 0.4);
            remainingEnemies -= enemyTypes[2].count;
        }
        
        // 重型敌人（从关卡8开始）
        if (level >= 8) {
            enemyTypes[3].count = remainingEnemies;
        } else {
            enemyTypes[0].count += remainingEnemies; // 剩余的都给基础敌人
        }
        
        return {
            totalEnemies,
            types: enemyTypes.filter(type => type.count > 0)
        };
    }

    // 获取关卡信息
    getLevelInfo(level) {
        const enemyConfig = this.generateEnemyConfig(level);
        
        return {
            level,
            name: level <= this.classicMaps.length ? `经典关卡 ${level}` : `随机关卡 ${level}`,
            description: this.getLevelDescription(level),
            enemyCount: enemyConfig.totalEnemies,
            enemyTypes: enemyConfig.types,
            difficulty: Math.min(level / 10, 1),
            bonusMultiplier: 1 + (level - 1) * 0.1
        };
    }

    // 获取关卡描述
    getLevelDescription(level) {
        if (level === 1) return "欢迎来到坦克大战！保护你的基地！";
        if (level <= 3) return "敌人数量增加，小心应对！";
        if (level <= 5) return "出现了快速敌人，注意躲避！";
        if (level <= 8) return "装甲敌人登场，需要多次攻击！";
        if (level <= 10) return "重型敌人出现，极其危险！";
        return "终极挑战，只有最强的指挥官才能通过！";
    }

    // 验证地图有效性
    validateMap(mapData) {
        // 检查基地是否存在
        if (!mapData.base) return false;
        
        // 检查出生点是否有效
        if (!mapData.playerSpawn || !mapData.enemySpawns || mapData.enemySpawns.length === 0) {
            return false;
        }
        
        // 检查是否有足够的空间
        const emptySpaces = this.countEmptySpaces(mapData.walls);
        if (emptySpaces < 100) return false; // 至少需要100个空格子
        
        return true;
    }

    // 计算空白区域数量
    countEmptySpaces(walls) {
        const wallSet = new Set();
        walls.forEach(wall => {
            wallSet.add(`${wall.x},${wall.y}`);
        });
        
        let emptyCount = 0;
        for (let x = 32; x < 800; x += 32) {
            for (let y = 32; y < 800; y += 32) {
                if (!wallSet.has(`${x},${y}`)) {
                    emptyCount++;
                }
            }
        }
        
        return emptyCount;
    }
}

// 创建全局地图生成器实例
window.mapGenerator = new MapGenerator();
