// 坦克大战主脚本
document.addEventListener('DOMContentLoaded', () => {
    console.log('=== Battle City Debug Start ===');
    console.log('DOMContentLoaded event fired');
    console.log('Available global objects:');
    console.log('- window.gameEngine:', typeof window.gameEngine);
    console.log('- window.gameStorage:', typeof window.gameStorage);
    console.log('- window.audioManager:', typeof window.audioManager);
    console.log('- window.effectsManager:', typeof window.effectsManager);
    console.log('- window.powerupManager:', typeof window.powerupManager);
    console.log('- window.mapGenerator:', typeof window.mapGenerator);

    // 调试状态更新函数
    function updateDebugStatus(status, extra = {}) {
        const statusEl = document.getElementById('debugStatusText');
        const gameEngineEl = document.getElementById('debugGameEngine');
        const canvasEl = document.getElementById('debugCanvas');
        const screenEl = document.getElementById('debugScreen');
        
        if (statusEl) statusEl.textContent = status;
        if (gameEngineEl) gameEngineEl.textContent = window.gameEngine ? 'Available' : 'Missing';
        if (canvasEl) {
            const canvas = document.getElementById('gameCanvas');
            canvasEl.textContent = canvas ? `${canvas.width}x${canvas.height}` : 'Missing';
        }
        if (screenEl) screenEl.textContent = currentScreen || 'Unknown';
        
        console.log(`Debug Status: ${status}`, extra);
    }

    // 初始状态更新
    updateDebugStatus('Script loaded, DOM ready');

    // 游戏状态管理
    let currentScreen = 'start';
    let selectedDifficulty = 'normal';
    let selectedSkin = 'classic';

    // 获取DOM元素
    const screens = {
        start: document.getElementById('startScreen'),
        skin: document.getElementById('skinScreen'),
        pause: document.getElementById('pauseScreen'),
        gameOver: document.getElementById('gameOverScreen'),
        levelComplete: document.getElementById('levelCompleteScreen'),
        game: document.getElementById('gameArea')
    };

    const buttons = {
        start: document.getElementById('startBtn'),
        skin: document.getElementById('skinBtn'),
        confirmSkin: document.getElementById('confirmSkinBtn'),
        cancelSkin: document.getElementById('cancelSkinBtn'),
        resume: document.getElementById('resumeBtn'),
        restart: document.getElementById('restartBtn'),
        mainMenu: document.getElementById('mainMenuBtn'),
        playAgain: document.getElementById('playAgainBtn'),
        backToMenu: document.getElementById('backToMenuBtn'),
        nextLevel: document.getElementById('nextLevelBtn'),
        pause: document.getElementById('pauseBtn'),
        restartGame: document.getElementById('restartGameBtn'),
        mute: document.getElementById('muteBtn')
    };

    // 初始化游戏
    function initGame() {
        console.log('=== initGame() called ===');
        updateDebugStatus('Initializing game...');
        
        console.log('Initializing game...');
        console.log('Available screens:', Object.keys(screens));
        console.log('Available buttons:', Object.keys(buttons));

        // 检查所有必需的元素是否存在
        for (const [name, element] of Object.entries(screens)) {
            if (!element) {
                console.error(`Screen element missing: ${name}`);
            } else {
                console.log(`Screen element found: ${name}`, element);
            }
        }

        for (const [name, element] of Object.entries(buttons)) {
            if (!element) {
                console.error(`Button element missing: ${name}`);
            } else {
                console.log(`Button element found: ${name}`, element);
            }
        }

        // 检查 canvas 元素
        const gameCanvas = document.getElementById('gameCanvas');
        const minimapCanvas = document.getElementById('minimapCanvas');
        console.log('Canvas elements:');
        console.log('- gameCanvas:', gameCanvas);
        console.log('- minimapCanvas:', minimapCanvas);

        updateDebugStatus('Loading settings...');

        // 加载保存的设置
        loadSettings();

        updateDebugStatus('Setting up event listeners...');

        // 设置事件监听器
        setupEventListeners();

        updateDebugStatus('Initializing skin selection...');

        // 初始化皮肤选择
        initSkinSelection();

        updateDebugStatus('Initializing difficulty selection...');

        // 初始化难度选择
        initDifficultySelection();

        updateDebugStatus('Showing start screen...');

        // 显示开始界面
        showScreen('start');

        updateDebugStatus('Game ready');
        console.log('Game initialized successfully');
        console.log('=== initGame() completed ===');
    }

    // 加载设置
    function loadSettings() {
        if (window.gameStorage) {
            selectedDifficulty = window.gameStorage.get('difficulty') || 'normal';
            selectedSkin = window.gameStorage.get('playerSkin') || 'classic';
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 开始游戏
        buttons.start?.addEventListener('click', startGame);
        
        // 皮肤选择
        buttons.skin?.addEventListener('click', () => showScreen('skin'));
        buttons.confirmSkin?.addEventListener('click', confirmSkinSelection);
        buttons.cancelSkin?.addEventListener('click', () => showScreen('start'));
        
        // 游戏控制
        buttons.pause?.addEventListener('click', pauseGame);
        buttons.resume?.addEventListener('click', resumeGame);
        buttons.restart?.addEventListener('click', restartGame);
        buttons.restartGame?.addEventListener('click', restartGame);
        buttons.mainMenu?.addEventListener('click', () => showScreen('start'));
        
        // 游戏结束
        buttons.playAgain?.addEventListener('click', startGame);
        buttons.backToMenu?.addEventListener('click', () => showScreen('start'));
        
        // 关卡完成
        buttons.nextLevel?.addEventListener('click', nextLevel);
        
        // 音效控制
        buttons.mute?.addEventListener('click', toggleMute);
        
        // 键盘快捷键
        document.addEventListener('keydown', handleGlobalKeydown);
    }

    // 初始化皮肤选择
    function initSkinSelection() {
        const skinGrid = document.getElementById('skinGrid');
        if (!skinGrid) return;

        const skins = window.gameEngine?.getAvailableSkins() || [];
        
        skinGrid.innerHTML = skins.map(skin => `
            <div class="skin-option ${skin.id === selectedSkin ? 'selected' : ''} ${!skin.unlocked ? 'locked' : ''}" 
                 data-skin="${skin.id}" ${skin.unlocked ? '' : 'title="' + skin.requirement + '"'}>
                <div class="skin-preview" style="background: ${getSkinColor(skin.id)};">
                    ${skin.icon}
                </div>
                <div class="skin-name">${skin.name}</div>
                ${!skin.unlocked ? '<div class="skin-lock">🔒</div>' : ''}
            </div>
        `).join('');

        // 添加点击事件
        skinGrid.querySelectorAll('.skin-option').forEach(option => {
            option.addEventListener('click', () => {
                if (option.classList.contains('locked')) return;
                
                skinGrid.querySelectorAll('.skin-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                selectedSkin = option.dataset.skin;
            });
        });
    }

    // 获取皮肤颜色
    function getSkinColor(skinId) {
        const colors = {
            classic: '#4ecdc4',
            military: '#2d5016',
            futuristic: '#667eea',
            stealth: '#2c3e50',
            golden: '#f7931e'
        };
        return colors[skinId] || colors.classic;
    }

    // 初始化难度选择
    function initDifficultySelection() {
        const difficultyButtons = document.querySelectorAll('.difficulty-btn');
        
        difficultyButtons.forEach(btn => {
            if (btn.dataset.level === selectedDifficulty) {
                btn.classList.add('active');
            }
            
            btn.addEventListener('click', () => {
                difficultyButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                selectedDifficulty = btn.dataset.level;
            });
        });
    }

    // 显示指定界面
    function showScreen(screenName) {
        console.log(`=== showScreen('${screenName}') called ===`);
        console.log(`Switching to screen: ${screenName}`);

        // 隐藏所有界面
        Object.values(screens).forEach(screen => {
            if (screen) {
                const oldDisplay = screen.style.display;
                screen.style.display = 'none';
                console.log(`Hidden screen: ${screen.id || 'unknown'} (was: ${oldDisplay})`);
            }
        });

        // 显示指定界面
        if (screens[screenName]) {
            screens[screenName].style.display = 'flex';
            currentScreen = screenName;
            console.log(`Screen ${screenName} is now visible`);
            console.log(`Screen element:`, screens[screenName]);
            console.log(`Screen computed display:`, window.getComputedStyle(screens[screenName]).display);
        } else {
            console.error(`Screen ${screenName} not found!`);
            console.log('Available screens:', Object.keys(screens));
        }
        
        // 特殊处理
        if (screenName === 'start') {
            // 停止游戏引擎
            if (window.gameEngine) {
                console.log('Stopping game engine...');
                window.gameEngine.stop();
            }
            
            // 停止音乐
            if (window.audioManager) {
                console.log('Stopping background music...');
                window.audioManager.stopBackgroundMusic();
            }
        }
        
        console.log(`=== showScreen('${screenName}') completed ===`);
    }

    // 开始游戏
    function startGame() {
        console.log('=== startGame() called ===');
        console.log('Starting game...');
        updateDebugStatus('Starting game...');

        if (!window.gameEngine) {
            console.error('Game engine not available');
            console.log('window.gameEngine type:', typeof window.gameEngine);
            console.log('window object keys:', Object.keys(window));
            updateDebugStatus('ERROR: Game engine not available');
            return;
        }

        console.log('Game engine is available:', window.gameEngine);
        console.log('Game engine type:', typeof window.gameEngine);
        updateDebugStatus('Game engine found, switching to game screen...');

        // 显示游戏界面
        console.log('Switching to game screen...');
        showScreen('game');

        // 等待一帧确保界面已显示
        requestAnimationFrame(() => {
            console.log('Animation frame callback executing...');
            updateDebugStatus('Checking game area visibility...');
            
            // 检查游戏区域是否可见
            const gameArea = document.getElementById('gameArea');
            console.log('Game area display style:', gameArea ? gameArea.style.display : 'element not found');
            console.log('Game area computed display:', gameArea ? window.getComputedStyle(gameArea).display : 'element not found');
            
            // 检查 canvas
            const canvas = document.getElementById('gameCanvas');
            console.log('Canvas element:', canvas);
            if (canvas) {
                console.log('Canvas width:', canvas.width);
                console.log('Canvas height:', canvas.height);
                console.log('Canvas context:', canvas.getContext('2d'));
            }

            updateDebugStatus('Applying game settings...');

            // 应用设置
            console.log('Applying settings...');
            window.gameEngine.setDifficulty(selectedDifficulty);
            window.gameEngine.setPlayerSkin(selectedSkin);

            console.log(`Applied settings - Difficulty: ${selectedDifficulty}, Skin: ${selectedSkin}`);

            updateDebugStatus('Starting new game...');

            // 开始新游戏
            console.log('Starting new game...');
            try {
                window.gameEngine.startNewGame(selectedDifficulty);
                console.log('Game engine startNewGame() completed');
                updateDebugStatus('Game started successfully!');
            } catch (error) {
                console.error('Error starting new game:', error);
                console.error('Error stack:', error.stack);
                updateDebugStatus(`ERROR: ${error.message}`);
            }

            // 启动游戏循环检查
            startGameStateMonitor();

            console.log('Game started successfully');
            console.log('=== startGame() completed ===');
        });
    }

    // 确认皮肤选择
    function confirmSkinSelection() {
        if (window.gameStorage) {
            window.gameStorage.set('playerSkin', selectedSkin);
        }
        showScreen('start');
    }

    // 暂停游戏
    function pauseGame() {
        if (window.gameEngine) {
            window.gameEngine.pauseGame();
            updatePauseScreen();
            showScreen('pause');
        }
    }

    // 恢复游戏
    function resumeGame() {
        if (window.gameEngine) {
            window.gameEngine.resumeGame();
            showScreen('game');
        }
    }

    // 重新开始游戏
    function restartGame() {
        if (window.gameEngine) {
            window.gameEngine.restartGame();
            showScreen('game');
        }
    }

    // 下一关
    function nextLevel() {
        if (window.gameEngine) {
            window.gameEngine.nextLevel();
            showScreen('game');
        }
    }

    // 切换静音
    function toggleMute() {
        if (window.audioManager) {
            const soundEnabled = window.audioManager.toggleSound();
            const muteBtn = buttons.mute;
            if (muteBtn) {
                muteBtn.textContent = soundEnabled ? '🔊 音效' : '🔇 音效';
            }
        }
    }

    // 更新暂停界面
    function updatePauseScreen() {
        if (!window.gameEngine) return;
        
        const gameState = window.gameEngine.getGameState();
        
        const pauseScore = document.getElementById('pauseScore');
        const pauseLevel = document.getElementById('pauseLevel');
        const pauseLives = document.getElementById('pauseLives');
        
        if (pauseScore) pauseScore.textContent = gameState.score.toLocaleString();
        if (pauseLevel) pauseLevel.textContent = gameState.level;
        if (pauseLives) pauseLives.textContent = gameState.lives;
    }

    // 更新游戏结束界面
    function updateGameOverScreen() {
        if (!window.gameEngine) return;
        
        const gameState = window.gameEngine.getGameState();
        
        const finalScore = document.getElementById('finalScore');
        const finalLevel = document.getElementById('finalLevel');
        const finalKills = document.getElementById('finalKills');
        const highScore = document.getElementById('highScore');
        
        if (finalScore) finalScore.textContent = gameState.score.toLocaleString();
        if (finalLevel) finalLevel.textContent = gameState.level;
        if (finalKills) finalKills.textContent = gameState.stats.enemiesKilled;
        
        if (highScore && window.gameStorage) {
            highScore.textContent = window.gameStorage.get('highScore').toLocaleString();
        }
        
        // 检查是否有新成就
        updateAchievements();
    }

    // 更新关卡完成界面
    function updateLevelCompleteScreen() {
        if (!window.gameEngine) return;
        
        const gameState = window.gameEngine.getGameState();
        const levelTime = Date.now() - gameState.stats.levelStartTime;
        
        const levelBonus = document.getElementById('levelBonus');
        const timeBonus = document.getElementById('timeBonus');
        const totalScore = document.getElementById('totalScore');
        
        const levelBonusValue = gameState.level * 1000;
        const timeBonusValue = Math.max(0, Math.floor((30000 - levelTime) / 100));
        
        if (levelBonus) levelBonus.textContent = `+${levelBonusValue}`;
        if (timeBonus) timeBonus.textContent = `+${timeBonusValue}`;
        if (totalScore) totalScore.textContent = gameState.score.toLocaleString();
    }

    // 更新成就显示
    function updateAchievements() {
        // 这里可以添加新成就的显示逻辑
        const achievementList = document.getElementById('achievementList');
        const newAchievements = document.getElementById('newAchievements');
        
        if (achievementList && newAchievements && window.gameStorage) {
            // 检查是否有新解锁的成就
            const achievements = window.gameStorage.getAchievements();
            const recentAchievements = Object.entries(achievements)
                .filter(([id, data]) => data.unlocked && Date.now() - data.unlockedAt < 60000) // 1分钟内解锁的
                .map(([id]) => id);
            
            if (recentAchievements.length > 0) {
                newAchievements.style.display = 'block';
                achievementList.innerHTML = recentAchievements.map(id => 
                    `<div class="achievement-item">🏆 ${getAchievementName(id)}</div>`
                ).join('');
            }
        }
    }

    // 获取成就名称
    function getAchievementName(achievementId) {
        const names = {
            first_kill: '首次击杀',
            sharpshooter: '神枪手',
            survivor: '幸存者',
            tank_destroyer: '坦克杀手',
            powerup_collector: '道具收集家'
        };
        return names[achievementId] || '未知成就';
    }

    // 游戏状态监控
    function startGameStateMonitor() {
        const monitor = () => {
            if (!window.gameEngine) return;
            
            const gameState = window.gameEngine.getGameState();
            
            switch (gameState.state) {
                case 'gameOver':
                    updateGameOverScreen();
                    showScreen('gameOver');
                    return; // 停止监控
                    
                case 'levelComplete':
                    updateLevelCompleteScreen();
                    showScreen('levelComplete');
                    return; // 停止监控
                    
                case 'paused':
                    if (currentScreen !== 'pause') {
                        updatePauseScreen();
                        showScreen('pause');
                    }
                    break;
                    
                case 'playing':
                    if (currentScreen !== 'game') {
                        showScreen('game');
                    }
                    break;
            }
            
            // 继续监控
            setTimeout(monitor, 100);
        };
        
        monitor();
    }

    // 全局键盘事件处理
    function handleGlobalKeydown(event) {
        // ESC键返回主菜单
        if (event.code === 'Escape') {
            if (currentScreen === 'game') {
                pauseGame();
            } else if (currentScreen !== 'start') {
                showScreen('start');
            }
        }
        
        // 回车键确认
        if (event.code === 'Enter') {
            if (currentScreen === 'start') {
                startGame();
            } else if (currentScreen === 'pause') {
                resumeGame();
            } else if (currentScreen === 'gameOver') {
                startGame();
            } else if (currentScreen === 'levelComplete') {
                nextLevel();
            }
        }
    }

    // 初始化游戏
    initGame();

    console.log('Battle City game ready!');
});
