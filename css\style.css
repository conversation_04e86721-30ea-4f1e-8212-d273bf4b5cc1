/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #2c3e50; /* 深蓝灰色背景 */
    color: #ecf0f1; /* 浅灰色文字 */
    overflow-x: hidden; /* 防止水平滚动条 */
}

header {
    background-color: #34495e; /* 稍浅的蓝灰色 */
    color: #ecf0f1;
    padding: 1em 0; /* 减少header高度 */
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    flex-shrink: 0; /* 防止header在flex布局中被压缩 */
}

header h1 {
    margin: 0;
    font-size: 1.8em; /* 调整标题大小 */
    font-weight: 300;
}

.layout-container {
    display: flex;
    flex-grow: 1; /* 占据剩余垂直空间 */
    overflow: hidden; /* 防止内部溢出导致滚动 */
}

nav#game-menu {
    width: 280px; /* 固定左侧菜单宽度 */
    background-color: #34495e;
    padding: 20px 15px;
    border-right: 2px solid #2c3e50;
    overflow-y: auto; /* 如果菜单项过多，允许垂直滚动 */
    flex-shrink: 0; /* 防止菜单在flex布局中被压缩 */
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

/* 游戏列表容器 - 改为垂直布局 */
nav#game-menu .game-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column; /* 垂直排列 */
    gap: 12px; /* 游戏项之间的间距 */
}

/* 单个游戏项 */
nav#game-menu .game-list-item a {
    display: block;
    text-decoration: none;
    color: #ecf0f1;
    background-color: #4a627a;
    padding: 12px 18px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.95em;
    transition: background-color 0.2s ease, padding-left 0.2s ease;
    border-left: 4px solid transparent; /* 用于激活状态指示 */
}

nav#game-menu .game-list-item a:hover {
    background-color: #5b7c99;
    color: #fff;
    padding-left: 22px; /* 轻微左移效果 */
}

nav#game-menu .game-list-item a.active {
    background-color: #1abc9c;
    color: #2c3e50;
    font-weight: 600;
    border-left: 4px solid #f1c40f; /* 激活时左边框高亮 (黄色) */
    padding-left: 18px; /* 保持padding一致 */
}

main {
    flex-grow: 1; /* 占据剩余水平空间 */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #233140;
    overflow: auto; /* 如果iframe内容溢出，允许滚动，但不希望有全局滚动条 */
}

#game-iframe {
    width: 100%; /* 宽度填充main区域 */
    height: 100%; /* 高度填充main区域 */
    max-width: 1280px; /* 限制最大宽度，避免过宽 */
    max-height: calc(100vh - 40px - 50px - 40px); /* 视口高度 - header - footer - main的padding */
    border: 2px solid #1abc9c;
    border-radius: 8px;
    box-shadow: 0 0 12px rgba(0,0,0,0.25);
    background-color: #000;
}

footer {
    background-color: #34495e;
    color: #bdc3c7;
    text-align: center;
    padding: 0.8em 0; /* 减少footer高度 */
    font-size: 0.85em;
    border-top: 1px solid #2c3e50;
    flex-shrink: 0; /* 防止footer在flex布局中被压缩 */
}

/* 响应式调整 */
@media (max-width: 768px) {
    .layout-container {
        flex-direction: column; /* 小屏幕上变回上下布局 */
    }
    nav#game-menu {
        width: 100%; /* 菜单宽度占满 */
        height: auto; /* 高度自适应 */
        max-height: 40vh; /* 限制最大高度，允许滚动 */
        border-right: none;
        border-bottom: 2px solid #2c3e50;
        padding: 10px;
    }
    nav#game-menu .game-list {
        flex-direction: row; /* 菜单项恢复水平排列 */
        flex-wrap: wrap; /* 允许换行 */
        justify-content: center;
        gap: 10px;
    }
    nav#game-menu .game-list-item a {
        padding: 8px 12px;
        font-size: 0.9em;
        border-left: none;
        border-bottom: 3px solid transparent; /* 改为底部边框指示激活 */
    }
    nav#game-menu .game-list-item a:hover {
        padding-left: 12px; /* 重置hover效果 */
        padding-bottom: 8px;
    }
    nav#game-menu .game-list-item a.active {
        border-left: none;
        border-bottom: 3px solid #f1c40f;
        padding-left: 12px;
        padding-bottom: 8px;
    }
    main {
        padding: 15px;
        height: auto; /* 高度自适应 */
    }
    #game-iframe {
        max-height: calc(100vh - 40px - 100px - 30px); /* 重新计算高度 */
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.6em;
    }
    nav#game-menu .game-list-item a {
        font-size: 0.85em;
        padding: 6px 10px;
    }
    footer {
        padding: 0.6em 0;
        font-size: 0.8em;
    }
} 