body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background-color: #f0f2f5;
    margin: 0;
    width: 350px;
    color: #1c1e21;
}

.container {
    padding: 15px;
}

h1 {
    font-size: 18px;
    text-align: center;
    margin-top: 0;
    margin-bottom: 15px;
    color: #050505;
}

.actions {
    display: flex;
    margin-bottom: 15px;
    gap: 10px;
}

#searchInput {
    flex-grow: 1;
    border: 1px solid #ccd0d5;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    outline: none;
}

#searchInput:focus {
    border-color: #1877f2;
    box-shadow: 0 0 0 2px #e7f3ff;
}

#syncButton {
    background-color: #1877f2;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
}

#syncButton:hover {
    background-color: #166fe5;
}

#bookmarksList {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dddfe2;
    border-radius: 6px;
}

#bookmarksList li {
    padding: 10px 12px;
    border-bottom: 1px solid #dddfe2;
    display: flex;
    align-items: center;
}

#bookmarksList li:last-child {
    border-bottom: none;
}

#bookmarksList li a {
    text-decoration: none;
    color: #050505;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#bookmarksList li a:hover {
    text-decoration: underline;
} 