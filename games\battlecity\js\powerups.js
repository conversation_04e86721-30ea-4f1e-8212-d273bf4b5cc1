// 道具系统
class PowerupManager {
    constructor() {
        this.powerups = [];
        this.activePowerups = new Map(); // 玩家当前激活的道具
        this.spawnTimer = 0;
        this.spawnInterval = 15000; // 15秒生成一个道具
        
        // 道具定义
        this.powerupTypes = {
            speed: {
                name: '速度提升',
                icon: '⚡',
                color: '#ffd23f',
                duration: 10000,
                description: '移动速度提升50%',
                rarity: 0.3
            },
            armor: {
                name: '装甲强化',
                icon: '🛡️',
                color: '#4ecdc4',
                duration: 15000,
                description: '减少受到的伤害',
                rarity: 0.25
            },
            fireRate: {
                name: '射速提升',
                icon: '🔥',
                color: '#ff6b35',
                duration: 12000,
                description: '射击速度提升100%',
                rarity: 0.3
            },
            multiShot: {
                name: '多重射击',
                icon: '💥',
                color: '#667eea',
                duration: 8000,
                description: '同时发射三发子弹',
                rarity: 0.15
            },
            health: {
                name: '生命恢复',
                icon: '❤️',
                color: '#e74c3c',
                duration: 0, // 瞬间效果
                description: '恢复一点生命值',
                rarity: 0.2
            },
            shield: {
                name: '能量护盾',
                icon: '🔮',
                color: '#9b59b6',
                duration: 20000,
                description: '免疫一次伤害',
                rarity: 0.1
            },
            freeze: {
                name: '冰冻敌人',
                icon: '❄️',
                color: '#3498db',
                duration: 5000,
                description: '冻结所有敌人',
                rarity: 0.15
            },
            bomb: {
                name: '炸弹',
                icon: '💣',
                color: '#e67e22',
                duration: 0, // 瞬间效果
                description: '摧毁屏幕上所有敌人',
                rarity: 0.05
            }
        };
    }

    // 更新道具系统
    update(deltaTime) {
        // 更新道具生成计时器
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval) {
            this.spawnRandomPowerup();
            this.spawnTimer = 0;
        }

        // 更新现有道具
        this.powerups = this.powerups.filter(powerup => {
            powerup.update(deltaTime);
            return powerup.active;
        });

        // 更新激活的道具持续时间
        for (const [type, data] of this.activePowerups.entries()) {
            data.remainingTime -= deltaTime;
            if (data.remainingTime <= 0) {
                this.deactivatePowerup(type);
            }
        }
    }

    // 生成随机道具
    spawnRandomPowerup() {
        // 随机选择道具类型（基于稀有度）
        const rand = Math.random();
        let cumulativeRarity = 0;
        let selectedType = 'speed';

        for (const [type, config] of Object.entries(this.powerupTypes)) {
            cumulativeRarity += config.rarity;
            if (rand <= cumulativeRarity) {
                selectedType = type;
                break;
            }
        }

        // 随机位置生成（避免在墙壁和基地附近）
        let x, y;
        let attempts = 0;
        do {
            x = Math.floor(Math.random() * 24) * 32 + 16; // 对齐到网格
            y = Math.floor(Math.random() * 24) * 32 + 16;
            attempts++;
        } while (attempts < 50 && this.isPositionBlocked(x, y));

        if (attempts < 50) {
            this.powerups.push(new Powerup(x, y, selectedType));
        }
    }

    // 检查位置是否被阻挡
    isPositionBlocked(x, y) {
        // 检查是否在基地附近
        const baseX = 384; // 假设基地在中心
        const baseY = 384;
        const distanceToBase = Math.sqrt((x - baseX) ** 2 + (y - baseY) ** 2);
        
        return distanceToBase < 100; // 基地周围100像素内不生成道具
    }

    // 手动生成道具（用于测试或特殊情况）
    spawnPowerup(x, y, type) {
        if (this.powerupTypes[type]) {
            this.powerups.push(new Powerup(x, y, type));
        }
    }

    // 玩家收集道具
    collectPowerup(powerup, player) {
        if (!powerup.active) return false;

        const config = this.powerupTypes[powerup.type];
        if (!config) return false;

        // 播放道具收集音效
        if (window.audioManager) {
            window.audioManager.playPowerup();
        }

        // 创建收集效果
        if (window.effectsManager) {
            window.effectsManager.createPowerupEffect(powerup.x, powerup.y);
            window.effectsManager.createTextEffect(
                powerup.x, powerup.y - 20, 
                config.name, 
                config.color, 
                16
            );
        }

        // 应用道具效果
        this.applyPowerupEffect(powerup.type, player);

        // 移除道具
        powerup.active = false;

        // 更新统计
        if (window.gameStorage) {
            window.gameStorage.incrementStatistic('powerupsCollected');
        }

        return true;
    }

    // 应用道具效果
    applyPowerupEffect(type, player) {
        const config = this.powerupTypes[type];
        
        switch (type) {
            case 'speed':
            case 'armor':
            case 'fireRate':
            case 'multiShot':
                // 持续性道具
                player.applyPowerup(type, config.duration);
                this.activatePowerup(type, config.duration);
                break;
                
            case 'health':
                // 瞬间效果：恢复生命
                player.health = Math.min(player.maxHealth, player.health + 1);
                break;
                
            case 'shield':
                // 护盾效果
                player.shield = true;
                this.activatePowerup(type, config.duration);
                setTimeout(() => {
                    player.shield = false;
                }, config.duration);
                break;
                
            case 'freeze':
                // 冰冻敌人效果
                this.activatePowerup(type, config.duration);
                // 这个效果需要在游戏主循环中处理
                break;
                
            case 'bomb':
                // 炸弹效果：摧毁所有敌人
                // 这个效果需要在游戏主循环中处理
                this.triggerBombEffect();
                break;
        }
    }

    // 激活道具
    activatePowerup(type, duration) {
        this.activePowerups.set(type, {
            remainingTime: duration,
            maxTime: duration
        });
    }

    // 取消激活道具
    deactivatePowerup(type) {
        this.activePowerups.delete(type);
    }

    // 触发炸弹效果
    triggerBombEffect() {
        // 这个方法会被游戏引擎调用来摧毁所有敌人
        this.bombTriggered = true;
    }

    // 检查是否有激活的道具
    hasPowerup(type) {
        return this.activePowerups.has(type);
    }

    // 获取激活道具的剩余时间
    getPowerupRemainingTime(type) {
        const data = this.activePowerups.get(type);
        return data ? data.remainingTime : 0;
    }

    // 获取所有激活的道具
    getActivePowerups() {
        const result = [];
        for (const [type, data] of this.activePowerups.entries()) {
            const config = this.powerupTypes[type];
            result.push({
                type,
                name: config.name,
                icon: config.icon,
                color: config.color,
                remainingTime: data.remainingTime,
                maxTime: data.maxTime,
                progress: data.remainingTime / data.maxTime
            });
        }
        return result;
    }

    // 渲染所有道具
    render(ctx) {
        this.powerups.forEach(powerup => powerup.render(ctx));
    }

    // 获取所有道具（用于碰撞检测）
    getPowerups() {
        return this.powerups.filter(p => p.active);
    }

    // 清除所有道具
    clear() {
        this.powerups = [];
        this.activePowerups.clear();
        this.spawnTimer = 0;
        this.bombTriggered = false;
    }

    // 检查并消费炸弹效果
    consumeBombEffect() {
        if (this.bombTriggered) {
            this.bombTriggered = false;
            return true;
        }
        return false;
    }
}

// 道具实体类
class Powerup extends Entity {
    constructor(x, y, type) {
        super(x, y, 24, 24);
        this.type = type;
        this.config = window.powerupManager?.powerupTypes[type] || {};
        this.animationTime = 0;
        this.bobOffset = 0;
        this.glowIntensity = 0;
        this.rotationAngle = 0;
        this.lifeTime = 30000; // 30秒后消失
        this.age = 0;
        
        // 生成时的特效
        if (window.effectsManager) {
            window.effectsManager.createPowerupEffect(x + this.width/2, y + this.height/2);
        }
    }

    update(deltaTime) {
        if (!this.active) return;

        this.animationTime += deltaTime;
        this.age += deltaTime;

        // 上下浮动效果
        this.bobOffset = Math.sin(this.animationTime * 0.003) * 3;

        // 发光效果
        this.glowIntensity = (Math.sin(this.animationTime * 0.005) + 1) * 0.5;

        // 旋转效果
        this.rotationAngle += deltaTime * 0.002;

        // 生命周期检查
        if (this.age >= this.lifeTime) {
            this.active = false;
            
            // 消失特效
            if (window.effectsManager) {
                const center = this.getCenter();
                window.effectsManager.createHitEffect(center.x, center.y, this.config.color);
            }
        }
    }

    render(ctx) {
        if (!this.active) return;

        ctx.save();

        const centerX = this.x + this.width / 2;
        const centerY = this.y + this.height / 2 + this.bobOffset;

        // 发光效果
        ctx.shadowColor = this.config.color || '#ffd23f';
        ctx.shadowBlur = 10 + this.glowIntensity * 10;

        // 移动到中心并旋转
        ctx.translate(centerX, centerY);
        ctx.rotate(this.rotationAngle);

        // 绘制道具背景
        ctx.fillStyle = this.config.color || '#ffd23f';
        ctx.globalAlpha = 0.8 + this.glowIntensity * 0.2;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();

        // 绘制道具边框
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.globalAlpha = 1;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2 - 1, 0, Math.PI * 2);
        ctx.stroke();

        // 绘制道具图标
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.config.icon || '?', 0, 0);

        ctx.restore();

        // 绘制道具名称（当接近消失时）
        if (this.age > this.lifeTime * 0.8) {
            const alpha = Math.sin((this.lifeTime - this.age) * 0.01);
            ctx.save();
            ctx.globalAlpha = Math.max(0, alpha);
            ctx.fillStyle = this.config.color || '#ffd23f';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(this.config.name || 'Unknown', centerX, centerY - 20);
            ctx.restore();
        }
    }

    // 获取边界框（考虑浮动效果）
    getBounds() {
        return {
            left: this.x,
            right: this.x + this.width,
            top: this.y + this.bobOffset,
            bottom: this.y + this.height + this.bobOffset
        };
    }
}

// 创建全局道具管理器实例
window.powerupManager = new PowerupManager();
