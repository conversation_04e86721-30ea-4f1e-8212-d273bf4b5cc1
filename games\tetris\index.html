<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块 | Tetris</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">🎮 俄罗斯方块</h1>
        
        <!-- 开始界面 -->
        <div id="startScreen" class="start-screen">
            <div class="start-content">
                <h2>🎯 经典俄罗斯方块</h2>
                <p>一个经典的俄罗斯方块游戏</p>
                <p>游戏界面右侧有详细的操作说明</p>
                <button id="startBtn" class="start-button">开始游戏</button>
            </div>
        </div>

        <!-- 游戏暂停界面 -->
        <div id="pauseScreen" class="pause-screen" style="display: none;">
            <div class="pause-content">
                <h2>⏸️ 游戏暂停</h2>
                <button id="resumeBtn" class="resume-button">继续游戏</button>
                <button id="restartBtn" class="restart-button">重新开始</button>
            </div>
        </div>

        <!-- 游戏结束界面 -->
        <div id="gameOverScreen" class="game-over-screen" style="display: none;">
            <div class="game-over-content">
                <h2>🎯 游戏结束</h2>
                <div class="final-score">
                    <span class="label">最终分数</span>
                    <span id="finalScore" class="score-value">0</span>
                </div>
                <div class="final-level">
                    <span class="label">达到等级</span>
                    <span id="finalLevel" class="level-value">1</span>
                </div>
                <button id="playAgainBtn" class="play-again-button">再玩一次</button>
            </div>
        </div>

        <!-- 主游戏界面 -->
        <div id="gameArea" class="game-area" style="display: none;">
            <!-- 左侧信息面板 -->
            <div class="info-panel left-panel">
                <div class="info-section">
                    <h3>下一个</h3>
                    <canvas id="nextCanvas" width="120" height="120"></canvas>
                </div>
                
                <div class="info-section">
                    <h3>暂存</h3>
                    <canvas id="holdCanvas" width="120" height="120"></canvas>
                </div>
            </div>

            <!-- 中间游戏区域 -->
            <div class="main-game">
                <canvas id="gameCanvas" width="300" height="600"></canvas>
                <div class="game-controls">
                    <button id="pauseBtn" class="control-btn">⏸️ 暂停</button>
                    <button id="restartGameBtn" class="control-btn">🔄 重新开始</button>
                </div>
            </div>

            <!-- 右侧统计面板 -->
            <div class="info-panel right-panel">
                <div class="stats-section">
                    <div class="stat-item">
                        <span class="stat-label">分数</span>
                        <span id="score" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">等级</span>
                        <span id="level" class="stat-value">1</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">行数</span>
                        <span id="lines" class="stat-value">0</span>
                    </div>
                </div>

                <div class="progress-section">
                    <h3>等级进度</h3>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progressLines">0</span> / <span id="progressTarget">10</span>
                    </div>
                </div>

                <div class="combo-section">
                    <div class="combo-display">
                        <span class="combo-label">连击</span>
                        <span id="combo" class="combo-value">0</span>
                    </div>
                </div>
                
                <div class="controls-section">
                    <h3>操作说明</h3>
                    <div class="control-list">
                        <div class="control-item">
                            <span class="key">←→</span>
                            <span class="action">移动</span>
                        </div>
                        <div class="control-item">
                            <span class="key">↓</span>
                            <span class="action">加速下落</span>
                        </div>
                        <div class="control-item">
                            <span class="key">↑</span>
                            <span class="action">旋转</span>
                        </div>
                        <div class="control-item">
                            <span class="key">X</span>
                            <span class="action">瞬间下落</span>
                        </div>
                        <div class="control-item">
                            <span class="key">C</span>
                            <span class="action">暂存方块</span>
                        </div>
                        <div class="control-item">
                            <span class="key">空格</span>
                            <span class="action">暂停</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 粒子效果容器 -->
    <div id="particleContainer" class="particle-container"></div>

    <script src="script.js"></script>
</body>
</html> 