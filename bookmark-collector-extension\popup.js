document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchInput');
    const syncButton = document.getElementById('syncButton');
    const bookmarksList = document.getElementById('bookmarksList');

    let allBookmarks = [];

    function renderBookmarks(bookmarks) {
        bookmarksList.innerHTML = '';
        if (!bookmarks || bookmarks.length === 0) {
            const li = document.createElement('li');
            li.textContent = '没有找到书签。请先同步。';
            bookmarksList.appendChild(li);
            return;
        }
        bookmarks.forEach(bookmark => {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.href = bookmark.url;
            a.textContent = bookmark.title;
            a.title = `${bookmark.title}\n${bookmark.url}`;
            a.target = '_blank';
            li.appendChild(a);
            bookmarksList.appendChild(li);
        });
    }

    function loadBookmarks() {
        chrome.storage.local.get(['bookmarks'], (result) => {
            if (result.bookmarks) {
                allBookmarks = result.bookmarks;
                renderBookmarks(allBookmarks);
            } else {
                renderBookmarks([]);
            }
        });
    }

    searchInput.addEventListener('input', () => {
        const searchTerm = searchInput.value.toLowerCase();
        const filteredBookmarks = allBookmarks.filter(bookmark => 
            bookmark.title.toLowerCase().includes(searchTerm) || 
            bookmark.url.toLowerCase().includes(searchTerm)
        );
        renderBookmarks(filteredBookmarks);
    });

    syncButton.addEventListener('click', () => {
        syncButton.textContent = '同步中...';
        syncButton.disabled = true;
        chrome.runtime.sendMessage({ type: 'sync-bookmarks' }, (response) => {
            if (response && response.status === 'success') {
                loadBookmarks(); // Re-load bookmarks after sync
            }
            syncButton.textContent = '同步书签';
            syncButton.disabled = false;
        });
    });

    // Load bookmarks when the popup is opened
    loadBookmarks();
}); 