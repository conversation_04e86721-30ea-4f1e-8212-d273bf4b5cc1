// 音频管理系统
class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = null;
        this.soundEnabled = true;
        this.musicEnabled = true;
        this.masterVolume = 0.7;
        this.musicVolume = 0.5;
        this.effectsVolume = 0.8;
        
        // 音频文件映射（使用Web Audio API生成的音效）
        this.soundDefinitions = {
            shoot: { frequency: 800, duration: 0.1, type: 'square' },
            explosion: { frequency: 150, duration: 0.3, type: 'sawtooth' },
            hit: { frequency: 400, duration: 0.15, type: 'triangle' },
            powerup: { frequency: 600, duration: 0.2, type: 'sine' },
            enemyDestroy: { frequency: 200, duration: 0.25, type: 'square' },
            baseHit: { frequency: 100, duration: 0.4, type: 'sawtooth' },
            levelComplete: { frequency: 880, duration: 0.5, type: 'sine' },
            gameOver: { frequency: 220, duration: 1.0, type: 'triangle' }
        };
        
        this.initAudioContext();
        this.loadSettings();
    }

    // 初始化音频上下文
    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            this.masterGain.gain.value = this.masterVolume;
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
            this.audioContext = null;
        }
    }

    // 从存储加载设置
    loadSettings() {
        if (window.gameStorage) {
            const settings = window.gameStorage.getSettings();
            this.masterVolume = settings.volume || 0.7;
            this.musicVolume = settings.musicVolume || 0.5;
            this.effectsVolume = settings.effectsVolume || 0.8;
            this.soundEnabled = window.gameStorage.get('soundEnabled') !== false;
            this.musicEnabled = window.gameStorage.get('musicEnabled') !== false;
            
            if (this.masterGain) {
                this.masterGain.gain.value = this.masterVolume;
            }
        }
    }

    // 恢复音频上下文（用户交互后）
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    // 生成音效
    generateSound(definition, volume = 1.0) {
        if (!this.audioContext || !this.soundEnabled) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.type = definition.type;
            oscillator.frequency.setValueAtTime(definition.frequency, this.audioContext.currentTime);
            
            const adjustedVolume = volume * this.effectsVolume;
            gainNode.gain.setValueAtTime(adjustedVolume, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + definition.duration);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + definition.duration);
            
            return oscillator;
        } catch (error) {
            console.warn('Failed to generate sound:', error);
        }
    }

    // 播放音效
    playSound(soundName, volume = 1.0) {
        if (!this.soundEnabled) return;
        
        const definition = this.soundDefinitions[soundName];
        if (definition) {
            this.generateSound(definition, volume);
        }
    }

    // 播放射击音效
    playShoot() {
        this.playSound('shoot', 0.6);
    }

    // 播放爆炸音效
    playExplosion() {
        this.playSound('explosion', 0.8);
    }

    // 播放击中音效
    playHit() {
        this.playSound('hit', 0.7);
    }

    // 播放道具音效
    playPowerup() {
        this.playSound('powerup', 0.9);
    }

    // 播放敌人被摧毁音效
    playEnemyDestroy() {
        this.playSound('enemyDestroy', 0.8);
    }

    // 播放基地被击中音效
    playBaseHit() {
        this.playSound('baseHit', 1.0);
    }

    // 播放关卡完成音效
    playLevelComplete() {
        this.playSound('levelComplete', 0.9);
    }

    // 播放游戏结束音效
    playGameOver() {
        this.playSound('gameOver', 0.8);
    }

    // 播放背景音乐（简单的循环音调）
    startBackgroundMusic() {
        if (!this.audioContext || !this.musicEnabled || this.music) return;

        try {
            // 创建简单的背景音乐循环
            this.music = {
                oscillators: [],
                gainNodes: [],
                isPlaying: true
            };

            const notes = [440, 523, 659, 784]; // A, C, E, G
            const noteDuration = 2.0;
            
            const playMusicLoop = () => {
                if (!this.music || !this.music.isPlaying) return;
                
                notes.forEach((frequency, index) => {
                    setTimeout(() => {
                        if (!this.music || !this.music.isPlaying) return;
                        
                        const oscillator = this.audioContext.createOscillator();
                        const gainNode = this.audioContext.createGain();
                        
                        oscillator.connect(gainNode);
                        gainNode.connect(this.masterGain);
                        
                        oscillator.type = 'sine';
                        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
                        
                        const volume = this.musicVolume * 0.1; // 很低的音量
                        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                        gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.1);
                        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + noteDuration - 0.1);
                        
                        oscillator.start(this.audioContext.currentTime);
                        oscillator.stop(this.audioContext.currentTime + noteDuration);
                        
                    }, index * noteDuration * 1000);
                });
                
                // 循环播放
                setTimeout(playMusicLoop, notes.length * noteDuration * 1000);
            };
            
            playMusicLoop();
            
        } catch (error) {
            console.warn('Failed to start background music:', error);
        }
    }

    // 停止背景音乐
    stopBackgroundMusic() {
        if (this.music) {
            this.music.isPlaying = false;
            this.music = null;
        }
    }

    // 切换音效开关
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        if (window.gameStorage) {
            window.gameStorage.set('soundEnabled', this.soundEnabled);
        }
        return this.soundEnabled;
    }

    // 切换音乐开关
    toggleMusic() {
        this.musicEnabled = !this.musicEnabled;
        if (window.gameStorage) {
            window.gameStorage.set('musicEnabled', this.musicEnabled);
        }
        
        if (this.musicEnabled) {
            this.startBackgroundMusic();
        } else {
            this.stopBackgroundMusic();
        }
        
        return this.musicEnabled;
    }

    // 设置主音量
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.value = this.masterVolume;
        }
        if (window.gameStorage) {
            window.gameStorage.updateSetting('volume', this.masterVolume);
        }
    }

    // 设置音乐音量
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (window.gameStorage) {
            window.gameStorage.updateSetting('musicVolume', this.musicVolume);
        }
    }

    // 设置音效音量
    setEffectsVolume(volume) {
        this.effectsVolume = Math.max(0, Math.min(1, volume));
        if (window.gameStorage) {
            window.gameStorage.updateSetting('effectsVolume', this.effectsVolume);
        }
    }

    // 获取音频状态
    getStatus() {
        return {
            soundEnabled: this.soundEnabled,
            musicEnabled: this.musicEnabled,
            masterVolume: this.masterVolume,
            musicVolume: this.musicVolume,
            effectsVolume: this.effectsVolume,
            contextState: this.audioContext ? this.audioContext.state : 'unavailable'
        };
    }

    // 播放复合音效（多个音调组合）
    playComplexSound(soundName) {
        switch (soundName) {
            case 'tankMove':
                // 坦克移动的低频音效
                this.generateSound({ frequency: 80, duration: 0.1, type: 'square' }, 0.3);
                break;
                
            case 'wallDestroy':
                // 墙壁被摧毁的音效
                this.generateSound({ frequency: 300, duration: 0.2, type: 'sawtooth' }, 0.6);
                setTimeout(() => {
                    this.generateSound({ frequency: 150, duration: 0.15, type: 'square' }, 0.4);
                }, 100);
                break;
                
            case 'victory':
                // 胜利音效
                const victoryNotes = [523, 659, 784, 1047]; // C, E, G, C
                victoryNotes.forEach((freq, index) => {
                    setTimeout(() => {
                        this.generateSound({ frequency: freq, duration: 0.3, type: 'sine' }, 0.7);
                    }, index * 150);
                });
                break;
        }
    }
}

// 创建全局音频管理器实例
window.audioManager = new AudioManager();
