# 系统模式

## 1. 模块通信

*   **门户与游戏 (`<iframe>`):**
    *   **加载游戏:** 门户通过设置 `<iframe>` 的 `src` 属性来加载游戏。
    *   **通信 (可选，按需实现):**
        *   **`postMessage` API:** 用于门户和嵌入的游戏之间进行双向通信。例如，游戏可以将得分或游戏结束事件通知给门户，门户可以向游戏发送控制命令 (如暂停、重置，尽管初始阶段可能不需要这么复杂)。
        *   **URL参数:** 门户可以在加载游戏时通过URL参数向游戏传递初始配置信息。
*   **游戏内部模块 (JavaScript):**
    *   **ES6模块 (`import`/`export`):** 优先使用ES6模块进行代码组织和依赖管理，实现高内聚低耦合。
    *   **事件驱动:** 对于游戏内不同模块间的解耦通信，可以实现一个简单的发布/订阅 (Pub/Sub) 事件系统，或者直接使用回调函数。
    *   **类与对象:** 通过类封装游戏实体 (Player, Enemy, Bullet) 和管理器 (GameManager, AssetLoader, AI模块)。模块间通过方法调用和属性访问进行直接交互。

## 2. 状态管理

*   **门户状态:** 主要由 `js/script.js` 管理，例如当前选中的游戏、`<iframe>` 的 `src` 等。状态相对简单，直接通过变量和DOM属性管理。
*   **游戏状态:** 每个游戏独立管理自身状态。
    *   **`GameManager` 对象 (或主游戏文件中的逻辑):** 负责管理核心游戏状态，如游戏阶段 (初始化、运行中、暂停、结束)、得分、生命值、当前关卡、AI生成模块的状态等。
    *   **游戏对象状态:** 每个游戏实体 (如 `Player`, `Enemy`) 内部维护自身状态 (如位置、速度、当前动画帧、是否激活等)。

## 3. 错误处理

*   **门户级别:**
    *   `js/script.js` 中的 `console.error()` 用于记录关键组件 (如菜单、iframe) 未找到的错误。
    *   对游戏列表加载 (如果从外部文件或API获取) 的潜在失败进行处理。
*   **游戏级别:**
    *   **资源加载失败:** 在资源加载器中实现错误处理，例如，如果图片或音频文件无法加载，可以尝试加载占位符资源或向控制台输出错误，避免游戏崩溃。
    *   **AI生成逻辑异常:** 对于AI生成关卡或内容时可能出现的逻辑错误或无限循环，应设置超时或迭代次数限制，并有备用方案 (如加载一个预设的默认关卡)。
    *   **运行时错误:** 使用 `try...catch` 块来捕获关键逻辑 (如游戏循环的 `update` 或 `render` 方法中) 可能发生的运行时错误，并通过 `console.error()` 报告，防止整个游戏卡死。

## 4. 资源管理 (单个游戏内)

*   **预加载:** 在游戏开始前，通过专门的加载器 (`AssetLoader` 或类似模块) 预加载所有必需的图片和音效资源。显示加载进度条或加载界面。
*   **资源缓存/池化 (按需):**
    *   对于频繁创建和销毁的对象 (如子弹)，可以考虑使用对象池模式来减少垃圾回收的压力。
    *   Howler.js 本身会对音频进行管理和缓存。

## 5. 输入处理

*   **门户:** 主要通过鼠标点击事件处理游戏选择菜单的交互。
*   **游戏:**
    *   通过 `window.addEventListener` 监听 `keydown` 和 `keyup` 事件来处理键盘输入。
    *   输入状态可以存储在一个对象中 (例如 `{ ArrowUp: false, ArrowDown: true, Space: false }`)，游戏对象的 `update` 方法根据这些状态来执行动作。
    *   确保输入监听器在游戏暂停或结束时能被正确移除或禁用，在游戏恢复时重新激活。

## 6. 游戏循环 (`requestAnimationFrame`)

*   **核心模式:**
    ```javascript
    let lastTime = 0;
    function gameLoop(timestamp) {
        const deltaTime = (timestamp - lastTime) / 1000; // deltaTime in seconds
        lastTime = timestamp;

        update(deltaTime); // 更新游戏状态和逻辑
        render();          // 绘制游戏画面

        requestAnimationFrame(gameLoop);
    }
    requestAnimationFrame(gameLoop); // 启动循环
    ```
*   **帧率无关的更新:** 使用 `deltaTime` 来更新游戏对象的位置、动画等，确保游戏速度在不同刷新率的显示器上保持一致。

## 7. AI逻辑集成模式

*   **AI内容生成器:** 作为独立的模块或类，在游戏初始化时或特定节点 (如新关卡开始) 被调用，生成数据 (如地图数组、敌人配置)。
*   **AI行为逻辑:** 通常在游戏对象的 `update` 方法中实现。AI决策可能依赖于其他游戏对象的状态 (如玩家位置) 和AI生成的内容数据 (如地图信息)。

## 8. 响应式设计 (门户)

*   **CSS媒体查询:** 主要通过CSS媒体查询调整门户UI在不同屏幕尺寸下的布局 (如菜单、iframe尺寸)。
*   **`<iframe>` 内容缩放:** 游戏本身是否需要响应式处理，取决于具体游戏的设计。如果游戏固定分辨率，`<iframe>` 区域的缩放可能导致模糊或黑边。理想情况下，游戏本身也能适应 `<iframe>` 的尺寸变化，但这会增加游戏开发的复杂性。初始阶段可固定 `<iframe>` 尺寸，后续根据需求优化。 