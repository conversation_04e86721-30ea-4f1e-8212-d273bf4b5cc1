# Project Brief: AI动态关卡生成的经典游戏门户

## 1. 项目概述

本项目旨在创建一个Web门户，用于承载一系列使用HTML5、CSS和JavaScript开发的经典FC风格游戏。门户提供统一的游戏选择界面和加载机制。

**核心特色:**
- **AI驱动的内容生成 (源自 `需求文档.md`):** 游戏的核心挑战内容，如关卡布局、地图结构、敌人波次、特定物品（如俄罗斯方块的序列）等，将通过AI相关算法或程序化生成逻辑动态创建。这旨在提供独特且可重玩性高的游戏体验。
- **智能行为实现 (源自 `详细设计.md`):** 游戏内非玩家角色（NPC）的行为，如敌人的移动模式、攻击策略、对玩家行为的反应等，将通过JavaScript逻辑直接实现，这些逻辑会融入AI思想，使NPC表现出一定的智能和适应性。

**关键区别与融合:**
- `需求文档.md` (v2.1) 更侧重于 **AI在关卡/内容生成方面的应用**。
- `详细设计.md` (v2.0) 更侧重于 **通过JS实现具有AI特点的NPC行为逻辑**。
- 本项目将 **融合两者**：既包含AI程序化生成关卡/内容，也致力于通过JS实现智能的NPC行为。

## 2. 主要目标

*   **构建Web门户容器:**
    *   实现清晰的游戏选择菜单。
    *   通过 `<iframe>` 安全地嵌入和展示各个游戏。
*   **开发多款经典FC风格游戏:**
    *   使用纯HTML5, CSS, 和JavaScript (ES6+) 实现。
    *   确保良好的Canvas渲染性能。
*   **集成AI驱动的动态内容生成:**
    *   为不同游戏设计和实现合适的程序化生成算法 (如迷宫生成、地图生成、敌人波次生成、道具序列生成等)。
*   **实现智能NPC行为:**
    *   为NPC设计和实现基于规则、状态机或简单启发式算法的行为逻辑，使其能对游戏环境和玩家行为做出反应。
*   **技术实现:**
    *   提供统一的输入处理机制。
    *   实现高效的碰撞检测。
    *   管理游戏资源 (如图形、音效)。
    *   构建健壮的游戏循环和状态管理。

## 3. 技术栈 (基于两份文档的共识和选定方案)

*   **核心语言:** HTML5, CSS3, JavaScript (ES6+)
*   **CSS方案:** 原生CSS + CSS Variables (Custom Properties)。保持轻量，门户UI需求简单。单个复杂游戏可自行决定是否引入Sass。
*   **JavaScript动画:**
    *   **门户UI:** 优先使用CSS Animations & Transitions。
    *   **游戏内帧动画 (角色、爆炸等):** JavaScript直接控制Canvas `drawImage`切换精灵图。
    *   **游戏内复杂编程式动画 (UI元素动态效果、非精灵序列动画):** 考虑引入 Anime.js (因其轻量)，GSAP作为备选。
*   **音频处理:** Howler.js (推荐 `howler.core.min.js` 除非需要高级特性)。简化音频管理，提供更好控制和兼容性。
*   **渲染:** 原生Canvas 2D API。

## 4. 关键模块与架构

### 4.1 游戏门户容器 (`index.html`, `css/style.css`, `js/script.js`)
*   **门户UI:** 页眉、游戏选择菜单、页脚。
*   **游戏展示区:** 使用 `<iframe>` 嵌入单个游戏，实现隔离。
*   **动态菜单生成:** JavaScript根据游戏列表数据动态生成菜单。
*   **游戏加载逻辑:** 点击菜单项更新 `<iframe>` 的 `src`。

### 4.2 单个游戏应用 (例如 `games/pacman/`)
*   **`index.html`:** 包含 `<canvas id="gameCanvas">`。
*   **`style.css` (可选):** 游戏内非Canvas UI元素的样式。
*   **`js/main.js` (或 `game.js`):** 游戏主逻辑。
    *   常量与配置。
    *   资源加载器 (可选模块 `assetLoader.js`)。
    *   游戏对象/类定义 (e.g., `Player.js`, `Enemy.js`, `Bullet.js`, `Map.js`)。
    *   **AI模块 (e.g., `aiLevelGenerator.js`, `aiEnemyBehavior.js`):**
        *   `aiLevelGenerator.js`: 包含关卡/地图/内容生成算法。
        *   `aiEnemyBehavior.js` (或在 `Enemy.js` 内): 包含NPC的智能行为逻辑。
    *   输入处理器 (可选模块 `inputHandler.js`)。
    *   碰撞检测模块 (可选模块 `collision.js`)。
    *   渲染逻辑 (clear, draw objects, draw UI)。
    *   游戏循环 (`requestAnimationFrame` based, with `update(deltaTime)` and `render(ctx)`)。
    *   初始化函数 (`init()`)。

## 5. AI实现思路概要

### 5.1 AI关卡/内容生成
*   **吃豆人:** 程序化迷宫生成 (如Recursive Backtracker, Prim's)。AI还可辅助豆子、能量豆的合理放置。
*   **俄罗斯方块:** 智能方块序列生成 (避免纯随机或7-bag的可预测性，根据局面启发式调整概率或简单预测)。
*   **坦克大战:** 程序化地图生成 (砖墙、钢墙、草地等，确保可玩性和基地保护)。

### 5.2 智能NPC行为 (传统游戏AI技术)
*   **吃豆人 (幽灵):**
    *   不同模式 (Scatter, Chase, Frightened) 下有不同目标。
    *   Chase模式下不同幽灵有独特索敌逻辑 (Blinky直接追，Pinky预判等)。
    *   移动决策基于到目标的距离和可行路径 (简化A*或优先方向选择)。
*   **坦克大战 (敌方坦克):**
    *   状态机逻辑 (Patrolling, Targeting Player, Targeting Base, Avoiding)。
    *   基于感知 (玩家位置、基地位置、地形) 进行决策。
    *   行为执行包括移动、瞄准、射击。

## 6. 主要风险点 (综合两文档)

*   AI关卡生成算法的复杂性与性能。
*   AI生成关卡的质量和可玩性控制。
*   技术栈学习曲线。
*   浏览器兼容性。

## 7. 文档参考

*   **需求文档:** `需求文档.md` (版本 2.1, 2023-10-28) - 侧重AI动态关卡生成。
*   **详细设计文档:** `详细设计.md` (版本 2.0, 2023-10-27) - 侧重JS实现AI驱动玩法。

本简报旨在融合两份文档的核心思想，为项目提供统一的概要和方向。 