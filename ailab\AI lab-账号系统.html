<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Lab 账号中心</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      overflow-x: hidden;
    }

    .background-pattern {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
      z-index: -1;
    }

    .header {
      background: rgba(30, 30, 47, 0.95);
      backdrop-filter: blur(20px);
      color: white;
      padding: 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #fff, #e0e7ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      z-index: 1;
    }

    .account-info {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 50px 20px;
      text-align: center;
      margin: 30px 20px;
      border-radius: 24px;
      box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      font-size: 18px;
      position: relative;
      overflow: hidden;
      animation: fadeInUp 0.8s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .account-info::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .account-info h2 {
      margin: 15px 0;
      font-size: 2rem;
      font-weight: 600;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .account-info p {
      color: #666;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
    }

    .badge {
      background: linear-gradient(135deg, #4ade80, #22c55e);
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
    }

    .account-switch {
      display: flex;
      justify-content: center;
      padding: 40px 20px;
      animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .profile-list {
      display: flex;
      gap: 50px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .profile-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #444;
      transition: transform 0.3s ease, filter 0.3s ease;
      cursor: pointer;
    }

    .profile-item:hover {
      transform: translateY(-5px);
      filter: brightness(1.1);
    }

    .avatar {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      border: 4px solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .profile-item:hover .avatar {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      transform: scale(1.05);
    }

    .add-circle {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 240, 255, 0.9));
      backdrop-filter: blur(10px);
      border: 2px dashed #bbb;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 36px;
      color: #888;
      transition: all 0.3s ease;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .add:hover .add-circle {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-color: transparent;
      transform: scale(1.05);
    }

    .name {
      margin-top: 12px;
      font-weight: 600;
      font-size: 16px;
    }

    .sub-action {
      font-size: 14px;
      color: #667eea;
      margin-top: 6px;
      font-weight: 500;
    }

    .card-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      padding: 20px;
      gap: 30px;
      max-width: 1200px;
      margin: 0 auto;
      animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 30px;
      border-radius: 20px;
      box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .card:hover {
      transform: translateY(-10px);
      box-shadow: 
        0 30px 60px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.3);
    }

    .card:hover::before {
      transform: scaleX(1);
    }

    .card h3 {
      margin-bottom: 12px;
      color: #1e1e2f;
      font-size: 1.4rem;
      font-weight: 700;
    }

    .card p {
      color: #666;
      font-size: 15px;
      line-height: 1.6;
      margin-bottom: 20px;
    }

    .card button {
      padding: 12px 24px;
      border: none;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 12px;
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;
      width: 100%;
      position: relative;
      overflow: hidden;
    }

    .card button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .card button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    }

    .card button:hover::before {
      left: 100%;
    }

    .floating-elements {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .floating-circle:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-circle:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .floating-circle:nth-child(3) {
      width: 40px;
      height: 40px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 2rem;
      }
      
      .profile-list {
        gap: 30px;
      }
      
      .card-container {
        grid-template-columns: 1fr;
        padding: 20px 15px;
      }
      
      .account-info {
        margin: 20px 15px;
        padding: 30px 20px;
      }
    }
  </style>
</head>
<body>
  <div class="background-pattern"></div>
  <div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
  </div>

  <div class="header">
    <h1>AI Lab · 我的账号</h1>
  </div>

  <div class="account-info">
    <h2>用户昵称: Leo</h2>
    <p>
      <span class="badge">车机账号已绑定</span>
      <span>当前积分: 1200 分</span>
    </p>
  </div>

  <section class="account-switch">
    <div class="profile-list">
      <div class="profile-item">
        <div class="avatar" style="background-image: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">E</div>
        <div class="name">edyy</div>
        <div class="sub-action">补充面容 / 声纹</div>
      </div>
      <div class="profile-item add">
        <div class="add-circle">+</div>
        <div class="name">添加成人</div>
      </div>
      <div class="profile-item add">
        <div class="add-circle">+</div>
        <div class="name">添加儿童</div>
      </div>
    </div>
  </section>

  <div class="card-container">
    <div class="card">
      <h3>💰 我的积分</h3>
      <p>查看积分余额、充值与使用记录，管理您的账户资产</p>
      <button onclick="window.location.href='credits-center.html'">进入积分中心</button>
    </div>
    <div class="card">
      <h3>⚡ 我的技能</h3>
      <p>查看已解锁应用功能与使用状态，探索更多AI能力</p>
      <button onclick="window.location.href='my-skills.html'">进入技能列表</button>
    </div>
    <div class="card">
      <h3>⚙️ 账号设置</h3>
      <p>查看账号绑定状态与同步信息，个性化配置体验</p>
      <button onclick="window.location.href='account-settings.html'">进入设置</button>
    </div>
  </div>
</body>
</html>