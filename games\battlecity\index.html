<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>坦克大战 | Battle City</title>
    <link rel="stylesheet" href="style.css">
    <!-- 游戏脚本 -->
    <script src="js/storage.js" defer></script>
    <script src="js/audio.js" defer></script>
    <script src="js/effects.js" defer></script>
    <script src="js/entities.js" defer></script>
    <script src="js/powerups.js" defer></script>
    <script src="js/mapGenerator.js" defer></script>
    <script src="js/gameEngine.js" defer></script>
    <script src="js/script.js" defer></script>
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">🚗 坦克大战</h1>
        
        <!-- 开始界面 -->
        <div id="startScreen" class="start-screen">
            <div class="start-content">
                <h2>🎯 经典坦克大战</h2>
                <p>驾驶坦克，保卫基地，消灭敌军！</p>
                <div class="game-features">
                    <div class="feature">🎮 经典玩法</div>
                    <div class="feature">🎨 多种皮肤</div>
                    <div class="feature">⚡ 强化道具</div>
                    <div class="feature">🏆 成就系统</div>
                </div>
                <div class="difficulty-selection">
                    <h3>选择难度</h3>
                    <div class="difficulty-buttons">
                        <button class="difficulty-btn" data-level="easy">简单</button>
                        <button class="difficulty-btn active" data-level="normal">普通</button>
                        <button class="difficulty-btn" data-level="hard">困难</button>
                    </div>
                </div>
                <button id="startBtn" class="start-button">开始游戏</button>
                <button id="skinBtn" class="skin-button">更换皮肤</button>
            </div>
        </div>

        <!-- 皮肤选择界面 -->
        <div id="skinScreen" class="skin-screen" style="display: none;">
            <div class="skin-content">
                <h2>🎨 选择坦克皮肤</h2>
                <div class="skin-grid" id="skinGrid">
                    <!-- 皮肤选项将通过JS动态生成 -->
                </div>
                <div class="skin-actions">
                    <button id="confirmSkinBtn" class="confirm-skin-btn">确认选择</button>
                    <button id="cancelSkinBtn" class="cancel-skin-btn">取消</button>
                </div>
            </div>
        </div>

        <!-- 游戏暂停界面 -->
        <div id="pauseScreen" class="pause-screen" style="display: none;">
            <div class="pause-content">
                <h2>⏸️ 游戏暂停</h2>
                <div class="pause-stats">
                    <div class="stat-row">
                        <span>当前分数:</span>
                        <span id="pauseScore">0</span>
                    </div>
                    <div class="stat-row">
                        <span>当前关卡:</span>
                        <span id="pauseLevel">1</span>
                    </div>
                    <div class="stat-row">
                        <span>剩余生命:</span>
                        <span id="pauseLives">3</span>
                    </div>
                </div>
                <div class="pause-actions">
                    <button id="resumeBtn" class="resume-button">继续游戏</button>
                    <button id="restartBtn" class="restart-button">重新开始</button>
                    <button id="mainMenuBtn" class="menu-button">返回主菜单</button>
                </div>
            </div>
        </div>

        <!-- 游戏结束界面 -->
        <div id="gameOverScreen" class="game-over-screen" style="display: none;">
            <div class="game-over-content">
                <h2 id="gameOverTitle">🎯 游戏结束</h2>
                <div class="final-stats">
                    <div class="stat-item">
                        <span class="label">最终分数</span>
                        <span id="finalScore" class="score-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="label">达到关卡</span>
                        <span id="finalLevel" class="level-value">1</span>
                    </div>
                    <div class="stat-item">
                        <span class="label">击毁敌军</span>
                        <span id="finalKills" class="kills-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="label">历史最高</span>
                        <span id="highScore" class="high-score-value">0</span>
                    </div>
                </div>
                <div class="achievements" id="newAchievements" style="display: none;">
                    <h3>🏆 新成就解锁！</h3>
                    <div id="achievementList"></div>
                </div>
                <button id="playAgainBtn" class="play-again-button">再玩一次</button>
                <button id="backToMenuBtn" class="back-menu-button">返回主菜单</button>
            </div>
        </div>

        <!-- 关卡完成界面 -->
        <div id="levelCompleteScreen" class="level-complete-screen" style="display: none;">
            <div class="level-complete-content">
                <h2>🎉 关卡完成！</h2>
                <div class="level-stats">
                    <div class="stat-row">
                        <span>关卡奖励:</span>
                        <span id="levelBonus" class="bonus-value">+1000</span>
                    </div>
                    <div class="stat-row">
                        <span>时间奖励:</span>
                        <span id="timeBonus" class="bonus-value">+500</span>
                    </div>
                    <div class="stat-row">
                        <span>总分数:</span>
                        <span id="totalScore" class="total-value">15000</span>
                    </div>
                </div>
                <button id="nextLevelBtn" class="next-level-button">下一关</button>
            </div>
        </div>

        <!-- 主游戏界面 -->
        <div id="gameArea" class="game-area" style="display: none;">
            <!-- 左侧信息面板 -->
            <div class="info-panel left-panel">
                <div class="info-section">
                    <h3>🎯 游戏状态</h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">关卡</span>
                            <span id="currentLevel" class="status-value">1</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">生命</span>
                            <span id="playerLives" class="status-value">3</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">敌军</span>
                            <span id="enemyCount" class="status-value">20</span>
                        </div>
                    </div>
                </div>
                
                <div class="info-section">
                    <h3>⚡ 当前道具</h3>
                    <div id="activePowerups" class="powerup-display">
                        <div class="no-powerups">暂无道具</div>
                    </div>
                </div>

                <div class="info-section">
                    <h3>🏆 成就进度</h3>
                    <div id="achievementProgress" class="achievement-display">
                        <!-- 成就进度将通过JS动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 中间游戏区域 -->
            <div class="main-game">
                <div class="game-header">
                    <div class="score-display">
                        <span class="score-label">分数</span>
                        <span id="gameScore" class="score-value">0</span>
                    </div>
                    <div class="base-health">
                        <span class="base-label">基地血量</span>
                        <div class="health-bar">
                            <div id="baseHealthBar" class="health-fill"></div>
                        </div>
                    </div>
                </div>
                
                <canvas id="gameCanvas" width="832" height="832"></canvas>
                
                <div class="game-controls">
                    <button id="pauseBtn" class="control-btn">⏸️ 暂停</button>
                    <button id="restartGameBtn" class="control-btn">🔄 重新开始</button>
                    <button id="muteBtn" class="control-btn">🔊 音效</button>
                </div>
            </div>

            <!-- 右侧统计面板 -->
            <div class="info-panel right-panel">
                <div class="stats-section">
                    <h3>📊 游戏统计</h3>
                    <div class="stat-item">
                        <span class="stat-label">击毁敌军</span>
                        <span id="killCount" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">发射子弹</span>
                        <span id="bulletsFired" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">命中率</span>
                        <span id="accuracy" class="stat-value">0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">游戏时间</span>
                        <span id="gameTime" class="stat-value">00:00</span>
                    </div>
                </div>

                <div class="controls-section">
                    <h3>🎮 操作说明</h3>
                    <div class="control-list">
                        <div class="control-item">
                            <span class="key">WASD</span>
                            <span class="action">移动坦克</span>
                        </div>
                        <div class="control-item">
                            <span class="key">空格</span>
                            <span class="action">发射子弹</span>
                        </div>
                        <div class="control-item">
                            <span class="key">P</span>
                            <span class="action">暂停游戏</span>
                        </div>
                        <div class="control-item">
                            <span class="key">R</span>
                            <span class="action">重新开始</span>
                        </div>
                        <div class="control-item">
                            <span class="key">M</span>
                            <span class="action">静音切换</span>
                        </div>
                    </div>
                </div>

                <div class="minimap-section">
                    <h3>🗺️ 小地图</h3>
                    <canvas id="minimapCanvas" width="150" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 粒子效果容器 -->
    <div id="particleContainer" class="particle-container"></div>

    <!-- 调试状态显示器 -->
    <div id="debugStatus" style="position: fixed; bottom: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; z-index: 9999; max-width: 300px;">
        <div>Status: <span id="debugStatusText">Initializing...</span></div>
        <div>GameEngine: <span id="debugGameEngine">Unknown</span></div>
        <div>Canvas: <span id="debugCanvas">Unknown</span></div>
        <div>Screen: <span id="debugScreen">Unknown</span></div>
    </div>

</body>
</html>
