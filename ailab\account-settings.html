<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Lab 账号设置</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      overflow-x: hidden;
    }

    .background-pattern {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
      z-index: -1;
    }

    .header {
      background: rgba(30, 30, 47, 0.95);
      backdrop-filter: blur(20px);
      color: white;
      padding: 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #fff, #e0e7ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      z-index: 1;
    }

    .back-button {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      z-index: 2;
    }

    .back-button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-50%) translateX(-2px);
    }

    .settings-container {
      max-width: 1000px;
      margin: 30px auto;
      padding: 0 20px;
    }

    .settings-section {
      margin-bottom: 30px;
      animation: fadeInUp 0.8s ease-out;
      animation-fill-mode: both;
    }

    .settings-section:nth-child(2) { animation-delay: 0.1s; }
    .settings-section:nth-child(3) { animation-delay: 0.2s; }
    .settings-section:nth-child(4) { animation-delay: 0.3s; }
    .settings-section:nth-child(5) { animation-delay: 0.4s; }
    .settings-section:nth-child(6) { animation-delay: 0.5s; }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .section-title {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 25px 35px;
      border-radius: 20px 20px 0 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e1e2f;
      box-shadow: 0 -15px 35px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .section-title::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .section-title::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 4s infinite;
    }

    .section-content {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 0 0 20px 20px;
      box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      padding: 30px;
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
    }

    .setting-item:last-child {
      border-bottom: none;
    }

    .setting-item:hover {
      background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.03), rgba(102, 126, 234, 0.05));
      margin: 0 -30px;
      padding-left: 30px;
      padding-right: 30px;
      border-radius: 12px;
    }

    .setting-left {
      flex: 1;
    }

    .setting-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e1e2f;
      margin-bottom: 4px;
    }

    .setting-description {
      font-size: 0.9rem;
      color: #666;
      line-height: 1.4;
    }

    .setting-right {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .status-connected {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08));
      color: #22c55e;
      border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .status-disconnected {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.08));
      color: #ef4444;
      border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .status-warning {
      background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.08));
      color: #f59e0b;
      border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .switch {
      position: relative;
      width: 50px;
      height: 24px;
      background: #ccc;
      border-radius: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .switch.active {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .switch::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .switch.active::before {
      transform: translateX(26px);
    }

    .action-button {
      padding: 8px 16px;
      border: none;
      border-radius: 12px;
      font-weight: 500;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .action-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .action-button:hover::before {
      left: 100%;
    }

    .button-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    .button-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .button-secondary {
      background: linear-gradient(135deg, rgba(156, 163, 175, 0.2), rgba(156, 163, 175, 0.1));
      color: #6b7280;
      border: 1px solid rgba(156, 163, 175, 0.3);
    }

    .button-secondary:hover {
      background: linear-gradient(135deg, rgba(156, 163, 175, 0.3), rgba(156, 163, 175, 0.15));
      transform: translateY(-1px);
    }

    .profile-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 2rem;
      font-weight: bold;
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
      margin-right: 20px;
    }

    .profile-info {
      flex: 1;
    }

    .profile-name {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e1e2f;
      margin-bottom: 5px;
    }

    .profile-id {
      font-size: 0.9rem;
      color: #888;
    }

    .floating-elements {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .floating-circle:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-circle:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .floating-circle:nth-child(3) {
      width: 40px;
      height: 40px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 2rem;
      }
      
      .back-button {
        left: 15px;
        padding: 10px 16px;
        font-size: 12px;
      }
      
      .settings-container {
        padding: 0 15px;
      }
      
      .section-title {
        padding: 20px 25px;
        font-size: 1.3rem;
      }
      
      .section-content {
        padding: 20px;
      }
      
      .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
      
      .setting-right {
        align-self: flex-end;
      }
      
      .profile-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-right: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="background-pattern"></div>
  <div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
  </div>

  <div class="header">
    <button class="back-button" onclick="window.location.href='AI lab-账号系统.html'">← 返回</button>
    <h1>⚙️ 账号设置</h1>
  </div>

  <div class="settings-container">
    <!-- 个人信息 -->
    <div class="settings-section">
      <div class="section-title">👤 个人信息</div>
      <div class="section-content">
        <div class="setting-item">
          <div class="profile-avatar">L</div>
          <div class="profile-info">
            <div class="profile-name">Leo</div>
            <div class="profile-id">用户ID: 1001234567</div>
          </div>
          <button class="action-button button-primary" onclick="editProfile()">编辑资料</button>
        </div>
      </div>
    </div>

    <!-- 账号绑定 -->
    <div class="settings-section">
      <div class="section-title">🔗 账号绑定</div>
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">车机账号</div>
            <div class="setting-description">绑定车载系统，享受无缝AI体验</div>
          </div>
          <div class="setting-right">
            <span class="status-badge status-connected">已绑定</span>
            <button class="action-button button-secondary" onclick="manageCarAccount()">管理</button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">手机号码</div>
            <div class="setting-description">用于接收验证码和重要通知</div>
          </div>
          <div class="setting-right">
            <span class="status-badge status-connected">已绑定</span>
            <button class="action-button button-secondary" onclick="changePhone()">更换</button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">微信账号</div>
            <div class="setting-description">快速登录和分享AI创作内容</div>
          </div>
          <div class="setting-right">
            <span class="status-badge status-disconnected">未绑定</span>
            <button class="action-button button-primary" onclick="bindWechat()">绑定</button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">面容识别</div>
            <div class="setting-description">快速身份验证，提升安全性</div>
          </div>
          <div class="setting-right">
            <span class="status-badge status-warning">需完善</span>
            <button class="action-button button-primary" onclick="setupFaceID()">设置</button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">声纹识别</div>
            <div class="setting-description">语音身份验证，个性化语音交互</div>
          </div>
          <div class="setting-right">
            <span class="status-badge status-warning">需完善</span>
            <button class="action-button button-primary" onclick="setupVoiceID()">设置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐私与安全 -->
    <div class="settings-section">
      <div class="section-title">🔒 隐私与安全</div>
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">数据加密</div>
            <div class="setting-description">对个人数据进行端到端加密保护</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">使用数据分析</div>
            <div class="setting-description">允许分析使用数据以改善服务体验</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">个性化推荐</div>
            <div class="setting-description">基于使用习惯提供个性化内容推荐</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">登录密码</div>
            <div class="setting-description">修改账号登录密码</div>
          </div>
          <div class="setting-right">
            <button class="action-button button-secondary" onclick="changePassword()">修改密码</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知设置 -->
    <div class="settings-section">
      <div class="section-title">🔔 通知设置</div>
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">系统通知</div>
            <div class="setting-description">接收系统更新和重要公告</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">积分变动通知</div>
            <div class="setting-description">积分获得或消费时发送通知</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">技能解锁提醒</div>
            <div class="setting-description">新技能可解锁时发送提醒</div>
          </div>
          <div class="setting-right">
            <div class="switch" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">营销推广</div>
            <div class="setting-description">接收活动推广和优惠信息</div>
          </div>
          <div class="setting-right">
            <div class="switch" onclick="toggleSwitch(this)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据同步 -->
    <div class="settings-section">
      <div class="section-title">☁️ 数据同步</div>
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">云端同步</div>
            <div class="setting-description">将设置和偏好同步到云端</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">多设备同步</div>
            <div class="setting-description">在所有设备间同步使用数据</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">自动备份</div>
            <div class="setting-description">自动备份重要数据和设置</div>
          </div>
          <div class="setting-right">
            <div class="switch active" onclick="toggleSwitch(this)"></div>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">同步频率</div>
            <div class="setting-description">设置数据同步的频率</div>
          </div>
          <div class="setting-right">
            <button class="action-button button-secondary" onclick="setSyncFrequency()">实时同步</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 其他设置 -->
    <div class="settings-section">
      <div class="section-title">🛠️ 其他设置</div>
      <div class="section-content">
        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">清除缓存</div>
            <div class="setting-description">清除应用缓存数据，释放存储空间</div>
          </div>
          <div class="setting-right">
            <button class="action-button button-secondary" onclick="clearCache()">清除</button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">导出数据</div>
            <div class="setting-description">导出个人数据和使用记录</div>
          </div>
          <div class="setting-right">
            <button class="action-button button-primary" onclick="exportData()">导出</button>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-left">
            <div class="setting-title">注销账号</div>
            <div class="setting-description">永久删除账号及所有相关数据</div>
          </div>
          <div class="setting-right">
            <button class="action-button button-secondary" onclick="deleteAccount()" style="color: #ef4444;">注销账号</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 切换开关状态
    function toggleSwitch(element) {
      element.classList.toggle('active');
    }

    // 编辑个人资料
    function editProfile() {
      alert('编辑个人资料功能即将开放');
    }

    // 管理车机账号
    function manageCarAccount() {
      alert('车机账号管理:\n- 当前绑定设备: Tesla Model 3\n- 绑定时间: 2024-01-10\n- 状态: 正常');
    }

    // 更换手机号
    function changePhone() {
      const newPhone = prompt('请输入新的手机号码:');
      if (newPhone) {
        alert(`手机号更换申请已提交，验证码将发送至 ${newPhone}`);
      }
    }

    // 绑定微信
    function bindWechat() {
      alert('正在跳转到微信授权页面...');
    }

    // 设置面容识别
    function setupFaceID() {
      alert('请按照系统提示完成面容录入\n\n1. 正面拍摄\n2. 左右转动头部\n3. 验证面容');
    }

    // 设置声纹识别
    function setupVoiceID() {
      alert('请按照系统提示录入声纹\n\n1. 朗读指定文本\n2. 重复3次录音\n3. 验证声纹');
    }

    // 修改密码
    function changePassword() {
      alert('密码修改页面即将开放');
    }

    // 设置同步频率
    function setSyncFrequency() {
      const options = ['实时同步', '每小时', '每天', '手动同步'];
      const current = '实时同步';
      alert(`当前同步频率: ${current}\n\n可选项:\n${options.join('\n')}`);
    }

    // 清除缓存
    function clearCache() {
      if (confirm('确认清除缓存吗？这将删除临时文件和历史数据。')) {
        alert('缓存清除成功！释放空间: 256 MB');
      }
    }

    // 导出数据
    function exportData() {
      alert('数据导出中...\n\n包含内容:\n- 个人信息\n- 使用记录\n- 积分历史\n- 技能数据\n\n预计完成时间: 2-3分钟');
    }

    // 注销账号
    function deleteAccount() {
      const confirm1 = confirm('⚠️ 警告：注销账号将永久删除所有数据！\n\n确认要继续吗？');
      if (confirm1) {
        const confirm2 = confirm('最后确认：\n\n删除后将无法恢复:\n- 所有个人数据\n- 积分和技能\n- 使用记录\n\n确定要注销吗？');
        if (confirm2) {
          alert('账号注销申请已提交\n\n注销将在7天后生效\n期间可登录取消注销');
        }
      }
    }
  </script>
</body>
</html> 