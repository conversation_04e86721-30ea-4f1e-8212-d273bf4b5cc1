body {
    margin: 0;
    background-color: #000;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh; /* 确保游戏容器在 iframe 中居中 */
    font-family: 'Arial', sans-serif;
    overflow: hidden; /* 防止游戏内部产生不必要的滚动条 */
}

#game-container {
    position: relative; /* 用于绝对定位子元素，如分数板 */
    border: 2px solid #333; /* 游戏区域边框 */
}

#pacmanCanvas {
    display: block; /* 移除canvas下方的额外空间 */
    background-color: #000; /* 画布背景 */
    /* 尺寸将在JS中设置 */
}

#score-board, #lives-board {
    position: absolute;
    color: #FFFF00; /* Pac-Man 黄色 */
    font-size: 1.2em;
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
    border-radius: 4px;
}

#score-board {
    top: 10px;
    left: 10px;
}

#lives-board {
    top: 10px;
    right: 10px;
} 