# 技术上下文

## 1. 开发环境

*   **IDE:** 推荐使用 Visual Studio Code (VS Code) 或其他支持良好前端开发的现代IDE。
*   **浏览器:** 主要开发和测试浏览器为最新版本的 Google Chrome。同时考虑在 Firefox 和 Edge 上进行兼容性测试。
*   **本地服务器:** 使用轻量级本地HTTP服务器进行开发，例如 VS Code 的 `Live Server` 扩展, Node.js 的 `http-server` 包, 或 Python 的 `http.server` 模块，以避免 `file:///` 协议带来的限制 (如模块加载、某些API的限制)。

## 2. 版本控制

*   **系统:** Git。
*   **托管平台:** 例如 GitHub, Gitee, GitLab 等 (根据实际选择)。
*   **分支策略 (建议):**
    *   `main` (或 `master`): 生产分支，保持稳定可用。
    *   `develop`: 主要开发分支，集成新功能。
    *   `feature/[feature-name]`: 功能开发分支，从 `develop` 创建，完成后合并回 `develop`。
    *   `fix/[issue-id]`: Bug修复分支。

## 3. 依赖管理

*   **前端库:** 对于像 Howler.js, Anime.js 这样的库，可以直接下载其 `min.js` 文件并包含在项目 `js/libs/` 目录中，或者通过CDN链接引入。本项目初期规模较小，暂不强制使用 npm/yarn 等包管理器，但如果项目复杂度增加或需要构建步骤，则应考虑引入。

## 4. 构建工具 (可选，初期可能不需要)

*   如果项目后续引入 Sass/SCSS，或者需要对 JavaScript 进行模块打包、代码压缩、ES6+ 转译等，可以考虑引入 Webpack 或 Parcel。
*   **初期目标:** 保持简单，不引入复杂构建流程，除非确实必要。

## 5. 代码质量与格式化

*   **Linter/Formatter:** 推荐使用 ESLint (JavaScript) 和 Prettier (通用代码格式化) 来保持代码风格一致性和早期错误检测。配置文件应纳入版本控制。

## 6. 外部AI服务集成 (智谱AI)

*   **服务提供商:** 智谱AI (BigModel)
*   **API 端点 (Endpoint):** `https://open.bigmodel.cn/api/paas/v4/`
*   **API Key:** `d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW`
    *   **注意:** 此 API Key 是敏感信息。在实际部署中，**不应将其硬编码到前端JavaScript代码中**。应考虑通过后端代理调用，或者在受控环境中作为环境变量配置。对于纯前端项目，如果必须由前端直接调用，需要仔细评估安全风险和API提供商的推荐做法 (例如是否有针对前端的特殊token获取机制或使用限制)。
*   **模型名称:** `glm-z1-flash`
*   **集成方式:**
    *   通过 JavaScript 的 `fetch` API 或 `XMLHttpRequest` 向该端点发送请求。
    *   请求体和头部需要遵循智谱AI的API文档规范。
    *   **错误处理与回退机制:**
        *   实现健壮的错误处理机制，应对API请求失败、超时、返回错误码等情况。
        *   **关键：** 在AI服务不可用或尚未完全集成时，游戏应有默认的回退逻辑。例如：
            *   对于迷宫生成，如果AI生成失败，则加载一个或多个预设的静态迷宫数据。
            *   对于敌人行为或方块序列，如果AI逻辑未准备好或调用失败，则使用简化的、基于规则的默认行为/序列。
        *   这种回退机制对于开发、测试和保证基本可玩性至关重要。

## 7. 部署

*   **静态网站托管:**
    *   GitHub Pages (简单免费)。
    *   Netlify, Vercel (提供CI/CD集成)。
    *   其他云服务商的静态托管服务 (如 AWS S3, Firebase Hosting)。
*   确保解决了 `file:///` 协议的限制，通常通过上述托管方式或本地服务器可以解决。

## 8. 浏览器兼容性目标

*   **主要目标:** 最新版本的 Chrome, Firefox, Edge。
*   **可选考虑:** Safari 最新版本。
*   不以支持旧版浏览器 (如 IE11) 为主要目标，除非有明确需求。 