# 技术设计文档：经典游戏门户与玩法实现

**版本：** 2.0
**日期：** 2023-10-27
**负责人：** [你的名字/团队名]

## 1. 引言

### 1.1 文档目的
本文档旨在详细描述"经典游戏门户与玩法实现"项目的技术架构、核心模块设计、具体游戏玩法的JavaScript实现思路（融入AI驱动的逻辑），以及所选用的前端技术栈（包括CSS方案和动画库）。本文档将作为开发团队进行具体实现和后续维护的主要技术参考。

### 1.2 项目概述
本项目旨在创建一个Web门户，用于承载一系列使用HTML5、CSS和JavaScript开发的经典FC游戏。这些游戏的核心特色在于其动态玩法（如敌人行为、关卡生成等）将通过JavaScript逻辑直接实现，这些逻辑的设计会体现出智能行为的思考，使其表现得如同具有AI。门户提供统一的游戏选择和加载界面。

### 1.3 范围
本文档覆盖游戏门户容器的设计、单个游戏应用的基础架构设计，以及选定代表性游戏中具体玩法（包含智能行为逻辑）的实现思路和相关技术选型。

### 1.4 读者对象
本项目的开发人员、测试人员以及对项目技术实现感兴趣的相关方。

## 2. 系统架构

### 2.1 总体架构图

```
+-----------------------------------+
| 用户浏览器 (Web Browser) |
+-----------------------------------+
^ (HTTP/HTTPS)
|
+-----------------------------------+
| 游戏门户容器 (index.html) |
| +-------------------------------+ |
| | 门户UI (HTML/CSS/JS) | |
| | +---------------------------+ | |
| | | 游戏选择菜单 (JS) | | |
| | +---------------------------+ | |
| | +---------------------------+ | |
| | | 游戏展示区 (<iframe>) | | |
| | +---------------------------+ | |
| +-------------------------------+ |
| ^ (src attribute) |
| | (Loads game HTML) |
+-----------------------------------+
|
v
+-----------------------------------+
| 单个游戏应用 (独立HTML文件) |
| (e.g., games/pacman/index.html)|
| +-------------------------------+ |
| | 游戏渲染 (Canvas API) | |
| +-------------------------------+ |
| | 游戏逻辑与智能行为 (JS) | | <--- 核心实现
| +-------------------------------+ |
| | 输入处理 (JavaScript) | |
| +-------------------------------+ |
| | 资源 (图片、音效 - 可选) | |
| +-------------------------------+ |
+-----------------------------------+
```

### 2.2 模块说明

*   **游戏门户容器 (Game Container):**
    *   **门户UI (HTML/CSS/JS):** 负责展示游戏列表、游戏标题、页脚等，并处理菜单交互。
    *   **游戏展示区 (`<iframe>`):** 嵌入并隔离单个游戏应用。

*   **单个游戏应用 (Individual Game Application):**
    *   **游戏渲染 (Canvas API):** 使用原生Canvas 2D API将游戏状态绘制到屏幕。
    *   **游戏逻辑与智能行为 (JavaScript):** 这是每个游戏的核心。包含：
        *   游戏规则的实现。
        *   角色行为控制（玩家和非玩家角色NPC）。
        *   NPC的行为将通过精心设计的算法和条件判断来实现，使其表现出智能和适应性（即"AI驱动的逻辑"）。**在初始阶段，我们将专注于实现这些基于规则的详细行为。**
        *   关卡动态生成逻辑（如果适用）。
        *   事件处理和状态管理。
    *   **输入处理 (JavaScript):** 监听并处理用户的键盘输入。
    *   **资源 (可选):** 游戏图片和音效。

## 3. 技术选型

### 3.1 核心技术
*   **HTML5:** 用于构建页面结构。
*   **CSS3:** 用于页面和游戏内UI（如果需要）的样式。
*   **JavaScript (ES6+):** 用于实现所有门户交互、游戏逻辑、智能行为和Canvas渲染。

### 3.2 CSS 方案
*   **选项1: 原生CSS + CSS Variables (Custom Properties):**
    *   **描述:** 直接编写CSS，利用CSS变量来管理颜色、字体、间距等，方便主题化和维护。
    *   **优点:** 轻量，无额外依赖，完全控制。
    *   **缺点:** 对于复杂布局和组件化可能需要更多手动编码。
*   **选项2: Sass/SCSS (CSS Preprocessor):**
    *   **描述:** 使用Sass/SCSS编写CSS，利用其嵌套、变量、混合 (Mixin)、函数等特性提高CSS代码的组织性和可维护性。需要编译步骤。
    *   **优点:** 代码更模块化，易于管理大型样式表，强大的特性集。
    *   **缺点:** 需要编译环境 (如Node-sass, Dart Sass)。
*   **选项3: Pico.css (Minimal CSS Framework):**
    *   **描述:** 一个非常轻量级的CSS框架，专注于为语义化的HTML提供优雅的默认样式，几乎不需要额外的CSS类。
    *   **优点:** 极小体积，快速上手，能迅速搭建一个美观的门户界面。
    *   **缺点:** 定制性相对较低，如果需要高度定制的视觉风格可能不完全满足。
*   **选定方案 (示例，请根据实际选择):** **原生CSS + CSS Variables**。理由：保持项目轻量级，且门户UI需求相对简单，CSS变量已能提供足够的灵活性。如果后续样式变得复杂，再考虑Sass。

### 3.3 JavaScript 动画库 (可选)
*   **选项1: CSS Animations & Transitions:**
    *   **描述:** 对于门户UI的简单动画（如菜单滑动、按钮悬停效果）或游戏内非关键的装饰性动画，优先使用CSS实现。
    *   **优点:** 性能好（浏览器硬件加速），声明式，代码简洁。
    *   **缺点:** 对于复杂的、基于JavaScript逻辑的动态游戏动画（如角色移动、粒子效果）不适用。
*   **选项2: GreenSock Animation Platform (GSAP) - `gsap.min.js`:**
    *   **描述:** 强大的专业JavaScript动画库，用于创建高性能、复杂的序列动画和交互动画。
    *   **优点:** 极佳的性能和灵活性，强大的时间轴控制，缓动函数丰富。
    *   **缺点:** 有一定学习曲线，对于FC风格的简单帧动画可能 overkill。
    *   **适用场景 (游戏内):** 如果游戏需要平滑的非精灵图序列的过渡动画（例如UI元素的动态出现/消失，特定技能的光效），GSAP会很有用。对于角色行走等基于精灵图的帧动画，通常由游戏逻辑直接控制Canvas `drawImage`的参数。
*   **选项3: Anime.js - `anime.min.js`:**
    *   **描述:** 轻量级的JavaScript动画库，API友好。
    *   **优点:** 小巧易用，功能也相对全面。
    *   **适用场景:** 类似于GSAP，但更轻量。
*   **选定方案 (示例，请根据实际选择):** **优先使用CSS Animations & Transitions** 用于门户UI。对于游戏内部的动画，主要通过**JavaScript直接控制Canvas绘图实现帧动画**（切换精灵图的不同帧）。如果确实需要复杂的编程式动画（非角色行走类），再考虑引入**Anime.js**因其轻量。

### 3.4 音频处理
*   **选项1: HTML5 `<audio>` 元素:**
    *   **描述:** 用于播放背景音乐和简单的音效。
    *   **优点:** 简单易用。
    *   **缺点:** 精确控制、同时播放多个音效、音效池管理等方面较弱。
*   **选项2: Web Audio API:**
    *   **描述:** 提供更强大和灵活的音频处理能力，适合游戏音效的精确控制、混音、音效节点处理等。
    *   **优点:** 功能强大，低延迟，适合复杂音频场景。
    *   **缺点:** API相对复杂。
*   **选项3: Howler.js - `howler.core.min.js` 或 `howler.min.js` (完整版):**
    *   **描述:** 一个JavaScript音频库，封装了Web Audio API和HTML5 Audio，提供了更简洁易用的API，并处理了浏览器兼容性问题。
    *   **优点:** API友好，跨浏览器，支持音效精灵、空间音频、淡入淡出等。
    *   **选定方案 (示例，请根据实际选择):** **Howler.js**。理由：简化音频管理，特别是处理多个短音效和背景音乐时，能提供更好的控制和兼容性。

## 4. 游戏门户容器设计

### 4.1 页面结构与样式
*   **HTML (`index.html`):** 结构同2.1所述。
*   **CSS (`style.css` 或通过Sass编译的CSS):**
    *   **布局:** 使用Flexbox或Grid进行整体页面布局（页眉、菜单、主内容区、页脚）。
    *   **菜单样式:** 设计清晰、易于点击的游戏列表项，可包含悬停效果（使用CSS Transitions）。
    *   **`<iframe>` 区域:** 明确定义尺寸，居中显示，可添加边框或阴影效果。
    *   **响应式考虑 (基础):** 使用媒体查询 (Media Queries) 调整小屏幕下的菜单布局（例如，菜单变为水平滚动或折叠式）。
*   **JavaScript (`script.js`):**
    *   实现动态菜单生成、游戏加载逻辑。
    *   **动画 (如果使用JS动画库):** 简单的菜单展开/折叠动画可以使用Anime.js或GSAP实现，若使用CSS则由CSS控制。

## 5. 单个游戏应用基础架构设计

### 5.1 核心文件与结构
*   **`index.html` (游戏主文件):** 包含`<canvas id="gameCanvas">`。
*   **`style.css` (游戏特定样式，可选):** 用于游戏内非Canvas绘制的UI元素（如得分、生命值文本的容器）。
*   **`main.js` (或 `game.js` - 游戏主逻辑文件):**
    *   **常量与配置:** 定义游戏参数（如Canvas尺寸、帧率目标、角色速度、颜色等）。
    *   **资源加载:** 函数用于预加载图片和音效（如果使用Howler.js，则由其处理音频加载）。
    *   **游戏对象/类定义:**
        *   `Player`
        *   `Enemy` (其`update`方法中将包含智能行为逻辑)
        *   `Bullet`
        *   `Item`
        *   `GameManager` (或直接在`main.js`中管理游戏流程)
    *   **输入处理:** `setupInputListeners()` 监听 `keydown` 和 `keyup` 事件。
    *   **碰撞检测:** `checkCollision(rect1, rect2)` 等辅助函数。
    *   **渲染逻辑:**
        *   `clearCanvas()`
        *   各个游戏对象的 `render(ctx)` 方法。
        *   绘制游戏背景、UI元素（得分、生命等）。
    *   **游戏循环:** `gameLoop(timestamp)`，包含 `update(deltaTime)` 和 `render()`。
        *   `update(deltaTime)`: 更新所有游戏对象的状态，执行智能行为逻辑，处理碰撞。
    *   **初始化函数:** `init()` 设置游戏，启动游戏循环。

### 5.2 智能行为的实现位置
智能行为逻辑（即所谓的"AI"）的**初始实现**将基于**规则**，并直接编写在相关的JavaScript对象或函数中，例如：
*   **敌人行为:** 在`Enemy`类的`update(deltaTime, player, mapData)`方法中，根据敌人当前状态、玩家位置、地图信息等，通过条件判断和算法（如状态机逻辑、简单路径选择、目标选择规则）来决定其移动、攻击等动作。
*   **程序化生成:** 如果游戏包含程序化内容生成（如《俄罗斯方块》的方块序列，《马里奥》的关卡片段），相关的生成算法将在专门的函数或模块中实现，并在适当的时候被游戏逻辑调用。

## 6. 具体游戏玩法实现思路 (示例，融入智能行为逻辑)

### 6.1 《吃豆人》(Pac-Man) - 幽灵行为逻辑

*   **文件结构 (示例):**
    *   `games/pacman/index.html`
    *   `games/pacman/style.css`
    *   `games/pacman/js/main.js` (主逻辑，游戏循环，吃豆人控制，豆子管理)
    *   `games/pacman/js/ghost.js` (Ghost类定义，包含其移动和目标选择逻辑)
    *   `games/pacman/js/map.js` (迷宫数据和绘制)
*   **`Ghost.js` 设计:**
    *   **属性:** `x`, `y`, `speed`, `color`, `mode` (`SCATTER`, `CHASE`, `FRIGHTENED`, `EATEN`), `targetTileX`, `targetTileY`, `scatterTargetX`, `scatterTargetY`, `currentDirection`, `nextDirection`。
    *   **`update(deltaTime, pacman, blinkyPosition, mapData)` 方法:**
        1.  **模式切换逻辑:** 根据全局游戏计时器或吃豆人状态（是否吃到大力丸）切换`mode`。
        2.  **目标设定:**
            *   **`SCATTER`模式:** `targetTileX/Y`设为各自固定的角落格子。
            *   **`CHASE`模式 (不同幽灵不同逻辑):**
                *   **Blinky:** `targetTileX/Y` = `pacman.tileX/Y`。
                *   **Pinky:** 计算吃豆人前方N格作为`targetTileX/Y`。
                *   **Inky:** 根据Blinky和吃豆人位置计算一个复杂的目标点。
                *   **Clyde:** 若离吃豆人远，目标为吃豆人；若近，目标为其`scatterTargetX/Y`。
            *   **`FRIGHTENED`模式:** `targetTileX/Y`可以设为一个随机的可达格子，或者简单地在交叉口随机选择方向。
        3.  **移动决策 (在交叉口或可以转向时):**
            *   获取当前格子周围可通行的方向（不包括来时的反方向，除非卡死或在`FRIGHTENED`模式）。
            *   对于每个可行方向，计算移动到该方向的下一个格子后，距离`targetTileX/Y`的曼哈顿距离。
            *   选择距离最小的那个方向作为`nextDirection`。
            *   （如果多个方向距离相同，按特定优先级选择，如上->左->下->右）。
        4.  **移动执行:** 根据`currentDirection`更新`x`, `y`坐标。到达格子中心时，如果`nextDirection`已确定，则更新`currentDirection = nextDirection`。
    *   **`render(ctx)` 方法:** 根据幽灵颜色和模式绘制不同状态的幽灵（如正常、蓝色、眼睛）。
*   **动画:** 幽灵的上下左右移动动画通过切换不同方向的精灵图帧实现，在`Ghost.render()`中处理。身体的摆动动画也可以通过几帧循环实现。

### 6.2 《俄罗斯方块》(Tetris) - 方块序列与下落逻辑

*   **文件结构 (示例):**
    *   `games/tetris/index.html`
    *   `games/tetris/style.css`
    *   `games/tetris/js/main.js` (主逻辑，游戏循环，板面管理，消除逻辑)
    *   `games/tetris/js/piece.js` (Piece类定义，方块形状、旋转、移动)
    *   `games/tetris/js/input.js`
*   **方块序列生成 (在`main.js`或`GameManager`中):**
    *   **7-Bag Randomizer 实现:**
        1.  `currentBag = []`
        2.  `fillBag()`: 如果`currentBag`为空，则将代表7种方块类型的标识（如数字0-6）打乱顺序后填入`currentBag`。
        3.  `getNextPieceType()`: 调用`fillBag()`，然后从`currentBag`中`pop()`出一个类型标识，返回。
        4.  根据返回的类型标识，在`Piece.js`中创建对应形状的新方块实例。
*   **`Piece.js` 设计:**
    *   **属性:** `x`, `y` (在游戏板面网格中的坐标)， `shape` (二维数组表示当前旋转状态的方块占据的格子)， `color`, `tetrominoType`。
    *   **`moveLeft()`, `moveRight()`, `moveDown()`, `rotate()` 方法:**
        *   这些方法会先尝试移动或旋转，然后调用板面管理模块的`isValidPosition(piece)`函数检查新位置是否合法（未出界、未与已固定方块碰撞）。
        *   如果合法，则更新`piece`的`x, y, shape`；否则，操作无效。
    *   `hardDrop()`: 快速将方块下落到底部。
*   **动画:** 方块的平滑下落（如果需要，而不是逐格跳）或消除行的动画效果可以使用JavaScript在`render()`中通过插值计算中间位置，或使用Anime.js/GSAP控制Canvas元素的属性（如果将每个小方格视为独立元素，但不推荐，性能差）。通常逐格移动和瞬间消除更符合FC风格。消除行动画可以通过几帧的闪烁或缩小效果在Canvas上绘制。

### 6.3 《坦克大战》(Battle City) - 敌方坦克行为逻辑

*   **文件结构 (示例):**
    *   `games/battlecity/index.html`
    *   `games/battlecity/js/main.js`
    *   `games/battlecity/js/tank.js` (Tank基类，PlayerTank和EnemyTank继承它)
    *   `games/battlecity/js/enemy_tank_logic.js` (包含不同类型敌方坦克的行为决策函数)
    *   `games/battlecity/js/bullet.js`
    *   `games/battlecity/js/map.js`
*   **`EnemyTank` 类 (继承自 `Tank`) 设计:**
    *   **额外属性:** `aiState` (`PATROLLING`, `TARGETING_PLAYER`, `TARGETING_BASE`, `AVOIDING`), `aiTimer` (用于控制行为持续时间或决策频率), `targetX`, `targetY`。
    *   **`update(deltaTime, playerTank, base, mapData, otherTanks)` 方法:**
        1.  `aiTimer`递减。
        2.  **状态机逻辑 (每隔一段时间或特定事件触发决策):**
            *   `if (aiTimer <= 0)`:
                *   **感知:** 获取玩家坦克位置、基地位置、自身周围地形信息。
                *   **决策 (示例逻辑，可放入`enemy_tank_logic.js`中的函数):**
                    *   **优先攻击玩家逻辑:** 如果玩家在一定范围内且路径相对清晰，设置`aiState = TARGETING_PLAYER`, `targetX/Y = playerTank.x/y`。
                    *   **攻击基地逻辑:** 如果玩家不在直接威胁范围内，且基地路径相对清晰，设置`aiState = TARGETING_BASE`, `targetX/Y = base.x/y`。
                    *   **巡逻逻辑:** 否则，设置`aiState = PATROLLING`，随机选择一个方向或目标点。
                    *   **避障逻辑:** 在向目标移动前，检查前方是否有障碍。如果有，尝试转向。如果持续受阻，`aiState = AVOIDING`，执行更复杂的避障（如尝试射击砖墙或尝试其他方向）。
                *   重置`aiTimer`为一个随机或固定的值。
        3.  **行为执行 (根据当前`aiState`):**
            *   `TARGETING_PLAYER/BASE`: 尝试将炮管朝向`targetX/Y`，如果朝向正确且冷却完毕，则射击。然后尝试向`targetX/Y`移动一小步。
            *   `PATROLLING`: 朝当前方向移动，如果遇到障碍或到达巡逻目标点，则重新决策方向。
            *   `AVOIDING`: 执行避障动作。
        4.  调用父类`Tank`的`update`处理通用移动和碰撞。
*   **动画:** 坦克的履带滚动动画通过循环播放几帧履带精灵图实现。爆炸动画使用多帧精灵图序列。这些都在各自的`render`方法中处理。

## 7. 渲染与游戏循环

*   **Canvas获取与设置:** 在`main.js`或`game.js`中获取Canvas元素和2D上下文。设置Canvas的`width`和`height`。
*   **游戏循环 (`requestAnimationFrame`):**
    *   `let lastTime = 0;`
    *   `function gameLoop(timestamp) {`
        *   `const deltaTime = timestamp - lastTime;`
        *   `lastTime = timestamp;`
        *   `update(deltaTime);`
        *   `render();`
        *   `requestAnimationFrame(gameLoop);`
    *   `}`
*   **渲染顺序:** 清空画布 -> 绘制背景/地图 -> 绘制游戏实体（敌人、玩家、道具）-> 绘制子弹 -> 绘制UI（得分、生命）-> （可选）绘制调试信息。

## 8. 风险与缓解措施

## 9. 部署考虑

## 10. 未来扩展性