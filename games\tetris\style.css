* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
    background-attachment: fixed;
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.game-title {
    font-size: 2.5em;
    margin: 0 0 20px 0;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 开始界面 */
.start-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.start-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.95), rgba(42, 82, 152, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
}

.start-content h2 {
    color: #4ecdc4;
    font-size: 2.2em;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
}

.controls-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 30px 0;
}

.control-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.key {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 1.1em;
    min-width: 60px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action {
    font-size: 0.9em;
    color: #b8b8b8;
}

.start-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.3em;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 15px rgba(102, 126, 234, 0.4);
    margin-top: 20px;
}

.start-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 20px rgba(102, 126, 234, 0.6);
}

/* 暂停界面 */
.pause-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.pause-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.95), rgba(42, 82, 152, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
}

.pause-content h2 {
    color: #ffd93d;
    margin-bottom: 30px;
    font-size: 2em;
}

.resume-button, .restart-button {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.restart-button {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.resume-button:hover, .restart-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* 游戏结束界面 */
.game-over-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.game-over-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(238, 90, 36, 0.95), rgba(255, 107, 107, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
}

.game-over-content h2 {
    color: #ffffff;
    margin-bottom: 30px;
    font-size: 2.2em;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.final-score, .final-level {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    font-size: 1.2em;
}

.score-value, .level-value {
    font-weight: bold;
    color: #ffd93d;
}

.play-again-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2em;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.play-again-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(102, 126, 234, 0.6);
}

/* 主游戏界面 */
.game-area {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    max-width: 1000px;
}

.info-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 180px;
}

.right-panel {
    min-width: 220px;
}

.info-section, .stats-section, .progress-section, .combo-section {
    margin-bottom: 25px;
}

.info-section h3, .progress-section h3 {
    color: #4ecdc4;
    margin: 0 0 15px 0;
    font-size: 1.1em;
    text-align: center;
}

#nextCanvas, #holdCanvas {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 2px solid rgba(78, 205, 196, 0.5);
    display: block;
    margin: 0 auto;
}

.main-game {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

#gameCanvas {
    background: rgba(0, 0, 0, 0.8);
    border: 3px solid #4ecdc4;
    border-radius: 10px;
    box-shadow: 0 0 30px rgba(78, 205, 196, 0.3);
}

.game-controls {
    display: flex;
    gap: 15px;
}

.control-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(102, 126, 234, 0.4);
}

/* 统计面板 */
.stat-item {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    color: #b8b8b8;
    font-size: 0.9em;
}

.stat-value {
    color: #ffd93d;
    font-weight: bold;
    font-size: 1.1em;
}

/* 进度条 */
.progress-bar {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    height: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    font-size: 0.9em;
    color: #b8b8b8;
}

/* 连击显示 */
.combo-display {
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 193, 7, 0.2));
    border-radius: 10px;
    padding: 15px;
    border: 2px solid rgba(255, 193, 7, 0.5);
}

.combo-label {
    display: block;
    color: #ffd93d;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.combo-value {
    display: block;
    font-size: 1.8em;
    font-weight: bold;
    color: #ff6b6b;
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

/* 操作说明面板 */
.controls-section {
    margin-top: 20px;
}

.controls-section h3 {
    color: #4ecdc4;
    margin-bottom: 15px;
    font-size: 1.1em;
    text-align: center;
}

.control-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-list .control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(78, 205, 196, 0.2);
    transition: all 0.3s ease;
}

.control-list .control-item:hover {
    background: rgba(78, 205, 196, 0.1);
    border-color: rgba(78, 205, 196, 0.4);
}

.control-list .key {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.8em;
    font-weight: bold;
    min-width: 32px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-list .action {
    color: #e0e0e0;
    font-size: 0.85em;
    flex: 1;
    text-align: right;
}

/* 粒子效果 */
.particle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 500;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd93d;
    border-radius: 50%;
    pointer-events: none;
    animation: particle-float 2s ease-out forwards;
}

@keyframes particle-float {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(0);
    }
}

.line-clear-effect {
    position: absolute;
    width: 100%;
    height: 30px;
    background: linear-gradient(90deg, transparent, #ffd93d, #ff6b6b, #4ecdc4, transparent);
    pointer-events: none;
    animation: line-clear 0.6s ease-out forwards;
}

@keyframes line-clear {
    0% {
        opacity: 1;
        transform: scaleX(0);
    }
    50% {
        opacity: 1;
        transform: scaleX(1);
    }
    100% {
        opacity: 0;
        transform: scaleX(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-area {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }
    
    .info-panel {
        width: 100%;
        max-width: 300px;
    }
    
    .left-panel {
        order: 2;
    }
    
    .main-game {
        order: 1;
    }
    
    .right-panel {
        order: 3;
    }
    
    #gameCanvas {
        width: 250px;
        height: 500px;
    }
    
    .controls-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* 特殊效果 */
.glow {
    animation: glow 1s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(78, 205, 196, 0.8);
    }
} 