// 视觉效果管理系统
class EffectsManager {
    constructor() {
        this.particles = [];
        this.effects = [];
        this.container = null;
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
        this.isRunning = false;
        
        this.init();
    }

    // 初始化效果系统
    init() {
        this.container = document.getElementById('particleContainer');
        if (!this.container) {
            console.warn('Particle container not found');
            return;
        }
        
        // 创建画布用于粒子效果
        this.canvas = document.createElement('canvas');
        this.canvas.style.position = 'fixed';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.pointerEvents = 'none';
        this.canvas.style.zIndex = '500';
        
        this.container.appendChild(this.canvas);
        this.ctx = this.canvas.getContext('2d');
        
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
        
        this.start();
    }

    // 调整画布大小
    resizeCanvas() {
        if (!this.canvas) return;
        
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }

    // 开始效果循环
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.animate();
    }

    // 停止效果循环
    stop() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    // 动画循环
    animate() {
        if (!this.isRunning) return;
        
        this.update();
        this.render();
        
        this.animationId = requestAnimationFrame(() => this.animate());
    }

    // 更新所有效果
    update() {
        const now = Date.now();
        
        // 更新粒子
        this.particles = this.particles.filter(particle => {
            particle.update();
            return particle.life > 0;
        });
        
        // 更新其他效果
        this.effects = this.effects.filter(effect => {
            effect.update();
            return !effect.finished;
        });
    }

    // 渲染所有效果
    render() {
        if (!this.ctx) return;
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染粒子
        this.particles.forEach(particle => particle.render(this.ctx));
        
        // 渲染其他效果
        this.effects.forEach(effect => effect.render(this.ctx));
    }

    // 创建爆炸效果
    createExplosion(x, y, size = 1, color = '#ff6b35') {
        const particleCount = Math.floor(15 * size);
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount + Math.random() * 0.5;
            const speed = (2 + Math.random() * 4) * size;
            const life = 0.5 + Math.random() * 0.5;
            
            this.particles.push(new ExplosionParticle(
                x, y, 
                Math.cos(angle) * speed, 
                Math.sin(angle) * speed,
                life, color, size
            ));
        }
        
        // 添加烟雾效果
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                this.particles.push(new SmokeParticle(
                    x + (Math.random() - 0.5) * 20,
                    y + (Math.random() - 0.5) * 20,
                    size
                ));
            }, i * 100);
        }
        
        // 屏幕震动
        this.screenShake(size * 5);
    }

    // 创建击中效果
    createHitEffect(x, y, color = '#ffd23f') {
        const sparkCount = 8;
        
        for (let i = 0; i < sparkCount; i++) {
            const angle = (Math.PI * 2 * i) / sparkCount + Math.random() * 0.3;
            const speed = 1 + Math.random() * 2;
            
            this.particles.push(new SparkParticle(
                x, y,
                Math.cos(angle) * speed,
                Math.sin(angle) * speed,
                0.3 + Math.random() * 0.2,
                color
            ));
        }
    }

    // 创建道具收集效果
    createPowerupEffect(x, y) {
        const colors = ['#ffd23f', '#4ecdc4', '#ff6b35', '#667eea'];
        
        for (let i = 0; i < 12; i++) {
            const angle = (Math.PI * 2 * i) / 12;
            const speed = 2 + Math.random() * 2;
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            this.particles.push(new GlowParticle(
                x, y,
                Math.cos(angle) * speed,
                Math.sin(angle) * speed,
                0.8 + Math.random() * 0.4,
                color
            ));
        }
    }

    // 创建子弹轨迹效果
    createBulletTrail(x, y, direction, color = '#ffd23f') {
        this.particles.push(new TrailParticle(x, y, direction, color));
    }

    // 创建坦克移动尘土效果
    createDustEffect(x, y) {
        if (Math.random() < 0.3) { // 30%概率产生尘土
            this.particles.push(new DustParticle(
                x + (Math.random() - 0.5) * 20,
                y + (Math.random() - 0.5) * 20
            ));
        }
    }

    // 屏幕震动效果
    screenShake(intensity = 5) {
        const gameCanvas = document.getElementById('gameCanvas');
        if (!gameCanvas) return;
        
        const originalTransform = gameCanvas.style.transform;
        const duration = 300;
        const startTime = Date.now();
        
        const shake = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1) {
                gameCanvas.style.transform = originalTransform;
                return;
            }
            
            const currentIntensity = intensity * (1 - progress);
            const x = (Math.random() - 0.5) * currentIntensity;
            const y = (Math.random() - 0.5) * currentIntensity;
            
            gameCanvas.style.transform = `translate(${x}px, ${y}px)`;
            
            requestAnimationFrame(shake);
        };
        
        shake();
    }

    // 创建文字效果
    createTextEffect(x, y, text, color = '#ffd23f', size = 20) {
        this.effects.push(new TextEffect(x, y, text, color, size));
    }

    // 创建关卡完成效果
    createLevelCompleteEffect() {
        const centerX = window.innerWidth / 2;
        const centerY = window.innerHeight / 2;
        
        // 创建庆祝粒子
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const x = centerX + (Math.random() - 0.5) * 200;
                const y = centerY + (Math.random() - 0.5) * 200;
                this.createPowerupEffect(x, y);
            }, i * 50);
        }
    }

    // 清除所有效果
    clear() {
        this.particles = [];
        this.effects = [];
        if (this.ctx) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }
}

// 粒子基类
class Particle {
    constructor(x, y, vx, vy, life, color) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.life = life;
        this.maxLife = life;
        this.color = color;
        this.size = 1;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life -= 0.016; // 假设60fps
        
        // 重力效果
        this.vy += 0.1;
        
        // 空气阻力
        this.vx *= 0.98;
        this.vy *= 0.98;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 爆炸粒子
class ExplosionParticle extends Particle {
    constructor(x, y, vx, vy, life, color, scale = 1) {
        super(x, y, vx, vy, life, color);
        this.size = (2 + Math.random() * 3) * scale;
        this.shrinkRate = this.size / life;
    }

    update() {
        super.update();
        this.size = Math.max(0, this.size - this.shrinkRate * 0.016);
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        
        // 创建径向渐变
        const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.size);
        gradient.addColorStop(0, this.color);
        gradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 火花粒子
class SparkParticle extends Particle {
    constructor(x, y, vx, vy, life, color) {
        super(x, y, vx, vy, life, color);
        this.size = 1 + Math.random() * 2;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 5;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 发光粒子
class GlowParticle extends Particle {
    constructor(x, y, vx, vy, life, color) {
        super(x, y, vx, vy, life, color);
        this.size = 2 + Math.random() * 3;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 烟雾粒子
class SmokeParticle extends Particle {
    constructor(x, y, scale = 1) {
        super(x, y, (Math.random() - 0.5) * 2, -Math.random() * 2, 1 + Math.random(), '#666');
        this.size = (5 + Math.random() * 10) * scale;
        this.growRate = this.size * 0.5;
    }

    update() {
        super.update();
        this.size += this.growRate * 0.016;
        this.vy -= 0.05; // 向上飘
    }

    render(ctx) {
        const alpha = (this.life / this.maxLife) * 0.3;
        ctx.save();
        ctx.globalAlpha = alpha;
        
        const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.size);
        gradient.addColorStop(0, this.color);
        gradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 轨迹粒子
class TrailParticle extends Particle {
    constructor(x, y, direction, color) {
        super(x, y, 0, 0, 0.2, color);
        this.direction = direction;
        this.length = 10;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(this.x, this.y);
        ctx.lineTo(
            this.x - Math.cos(this.direction) * this.length,
            this.y - Math.sin(this.direction) * this.length
        );
        ctx.stroke();
        ctx.restore();
    }
}

// 尘土粒子
class DustParticle extends Particle {
    constructor(x, y) {
        super(x, y, (Math.random() - 0.5) * 1, -Math.random() * 0.5, 0.5, '#8B4513');
        this.size = 1 + Math.random() * 2;
    }

    render(ctx) {
        const alpha = (this.life / this.maxLife) * 0.5;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

// 文字效果
class TextEffect {
    constructor(x, y, text, color, size) {
        this.x = x;
        this.y = y;
        this.text = text;
        this.color = color;
        this.size = size;
        this.life = 2.0;
        this.maxLife = 2.0;
        this.vy = -1;
        this.finished = false;
    }

    update() {
        this.y += this.vy;
        this.life -= 0.016;
        this.finished = this.life <= 0;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.font = `bold ${this.size}px Arial`;
        ctx.textAlign = 'center';
        ctx.shadowColor = 'black';
        ctx.shadowBlur = 3;
        ctx.fillText(this.text, this.x, this.y);
        ctx.restore();
    }
}

// 创建全局效果管理器实例
window.effectsManager = new EffectsManager();
