# 手势追踪星空游戏

这是一个交互式的Web应用程序，它使用您计算机的摄像头来追踪您的手部动作。您可以用大拇指和食指组成一个"弹弓"，接住并弹射掉落的星星。

## 技术栈

该项目采用以下技术构建：

-   **HTML5**: 提供网页的基本结构，包括 `<video>` 和 `<canvas>` 元素。
-   **CSS3**: 用于美化应用程序，创建布局，并添加阴影和颜色等视觉效果。
-   **JavaScript (ES6+)**: 驱动核心应用程序逻辑，包括用户交互、游戏状态管理以及协调不同的库。
-   **[MediaPipe Hands](https://google.github.io/mediapipe/solutions/hands.html)**: 来自谷歌的高保真手部和手指追踪库。它用于从摄像头视频流中实时检测手部的21个3D关键点。项目当前使用此库进行手势识别。
-   **[Matter.js](https://brm.io/matter-js/)**: 一个用于Web的2D刚体物理引擎。它用于模拟下落星星的物理效果（如重力、碰撞）以及可交互的弹弓。
-   **Webcam API (`getUserMedia`)**: 浏览器 API，用于访问用户的摄像头并传输视频流以进行手部追踪。

## 工作原理

1.  应用程序使用浏览器的 `getUserMedia` API 访问摄像头。
2.  视频流被传递给 MediaPipe Hands 库，该库会分析每一帧并检测用户手部关键点的位置。
3.  使用大拇指和食指指尖的坐标来创建和更新屏幕上的弯曲"弹弓"线。这个弹弓在 Matter.js 物理世界中由几个通过约束连接的小物体组成。
4.  星星作为 Matter.js 物理体周期性生成，并在模拟重力的作用下向下坠落。
5.  当星星与弹弓碰撞时，物理引擎会计算产生的反弹效果。
6.  所有内容都渲染在 HTML5 `<canvas>` 元素上，视频作为背景，游戏元素（星星、弹弓）绘制在顶层。

## 开发工具

此项目的代码主要由 AI 编程助手（如 Cursor）辅助编写。

## 如何运行

只需在支持 Webcam API 的现代 Web 浏览器（如 Chrome、Firefox 或 Edge）中打开 `index.html` 文件。当提示时，您需要授予摄像头权限。
