document.addEventListener('DOMContentLoaded', () => {
    const ring = document.querySelector('.ring');
    const cards = document.querySelectorAll('.card');
    const cardCount = cards.length;
    const angle = 360 / cardCount;
    const radius = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--ring-radius'));

    let isDragging = false;
    let startX, currentX;
    let rotationY = 0;
    let velocityY = 0;
    const friction = 0.95; 

    function positionCards() {
        cards.forEach((card, index) => {
            const cardAngle = angle * index;
            const transform = `rotateY(${cardAngle}deg) translateZ(${radius}px)`;
            card.style.transform = transform;
            // Store initial transform to combine with hover effect
            card.style.setProperty('--transform-initial', transform);
        });
    }

    function updateRotation() {
        ring.style.transform = `rotateX(-15deg) rotateY(${rotationY}deg)`;
    }

    function snapToCard() {
        const currentRotation = rotationY % 360;
        const closestAngle = Math.round(currentRotation / angle) * angle;
        
        // We need to find the shortest path to the target angle.
        // For example, from 350deg to 0/360deg.
        let diff = closestAngle - currentRotation;
        if (diff > 180) diff -= 360;
        if (diff < -180) diff += 360;

        rotationY += diff;
        
        ring.style.transition = 'transform 0.5s cubic-bezier(0.76, 0, 0.24, 1)';
        updateRotation();
    }

    // --- Event Listeners ---

    ring.addEventListener('mousedown', (e) => {
        e.preventDefault();
        isDragging = true;
        startX = e.clientX;
        ring.style.transition = 'none'; // Disable transition during drag for immediate response
    });

    window.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        currentX = e.clientX;
        const deltaX = currentX - startX;
        
        // Adjust sensitivity of rotation
        const rotationSpeedFactor = 0.25;
        rotationY += deltaX * rotationSpeedFactor;
        
        updateRotation();
        startX = currentX;
    });

    window.addEventListener('mouseup', () => {
        if (!isDragging) return;
        isDragging = false;
        snapToCard();
    });

    // For touch devices
    ring.addEventListener('touchstart', (e) => {
        isDragging = true;
        startX = e.touches[0].clientX;
        ring.style.transition = 'none';
    }, { passive: true });

    window.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        currentX = e.touches[0].clientX;
        const deltaX = currentX - startX;
        const rotationSpeedFactor = 0.25;
        rotationY += deltaX * rotationSpeedFactor;
        updateRotation();
        startX = currentX;
    }, { passive: true });

    window.addEventListener('touchend', () => {
        if (!isDragging) return;
        isDragging = false;
        snapToCard();
    });


    // Initial setup
    positionCards();
    updateRotation();
    // Initial snap to the card marked as 'priority' which should be at the front
    cards.forEach((card, index) => {
        if (card.classList.contains('priority')) {
            rotationY = -angle * index;
            updateRotation();
        }
    });
}); 