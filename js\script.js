// 游戏门户主脚本

document.addEventListener('DOMContentLoaded', () => {
    console.log('游戏门户脚本已加载');

    const gameMenuList = document.querySelector('#game-menu .game-list');
    const gameIframe = document.getElementById('game-iframe');
    let currentActiveLink = null;

    // 示例游戏列表 - 后续可以从配置文件或API加载
    const games = [
        {
            name: '吃豆人 (Pac-Man)',
            path: 'games/pacman/index.html', // 假设的路径
            id: 'pacman',
            description: '经典的迷宫追逐游戏。'
        },
        {
            name: '俄罗斯方块 (Tetris)',
            path: 'games/tetris/index.html', // 假设的路径
            id: 'tetris',
            description: '挑战你的空间排列能力。'
        },
        {
            name: '坦克大战 (Battle City)',
            path: 'games/battlecity/index.html', // 假设的路径
            id: 'battlecity',
            description: '驾驶坦克，保卫基地。'
        },
        {
            name: '贪吃蛇 (Snake)',
            path: 'games/snake/index.html', // 假设的路径
            id: 'snake',
            description: '控制小蛇不断变长。'
        },
        {
            name: '接星星',
            path: 'games/star/index.html',
            id: 'star-catcher',
            description: '用手接住从天而降的星星。'
        }
        // 添加更多游戏...
    ];

    function loadGame(gamePath, linkElement) {
        if (gamePath) {
            gameIframe.src = gamePath;
            if (currentActiveLink) {
                currentActiveLink.classList.remove('active');
            }
            linkElement.classList.add('active');
            currentActiveLink = linkElement;
        } else {
            gameIframe.src = ''; // 如果路径为空，则清空iframe
            if (currentActiveLink) {
                currentActiveLink.classList.remove('active');
                currentActiveLink = null;
            }
        }
    }

    function createGameMenu() {
        if (!gameMenuList) {
            console.error('Game menu list container not found!');
            return;
        }
        gameMenuList.innerHTML = ''; // 清空现有列表

        if (games.length === 0) {
            gameMenuList.innerHTML = '<p>暂无游戏</p>';
            return;
        }

        games.forEach(game => {
            const listItem = document.createElement('li');
            listItem.classList.add('game-list-item');

            const link = document.createElement('a');
            link.href = `#${game.id}`;
            link.textContent = game.name;
            link.dataset.path = game.path;
            link.title = game.description || game.name; // 添加描述作为title

            link.addEventListener('click', (event) => {
                event.preventDefault();
                loadGame(game.path, link);
            });

            listItem.appendChild(link);
            gameMenuList.appendChild(listItem);
        });
    }

    // 初始化
    createGameMenu();

    // (可选) 默认加载第一个游戏或欢迎页面
    if (games.length > 0 && gameMenuList.querySelector('.game-list-item a')) {
        // 默认不主动加载任何游戏，让用户选择
        // loadGame(games[0].path, gameMenuList.querySelector('.game-list-item a'));
        gameIframe.src = 'about:blank'; // 或者一个欢迎页面 game-welcome.html
    } else {
        gameIframe.src = 'about:blank';
    }
}); 