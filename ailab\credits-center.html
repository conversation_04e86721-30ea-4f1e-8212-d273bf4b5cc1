<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Lab 积分中心</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      overflow-x: hidden;
    }

    .background-pattern {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
      z-index: -1;
    }

    .header {
      background: rgba(30, 30, 47, 0.95);
      backdrop-filter: blur(20px);
      color: white;
      padding: 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #fff, #e0e7ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      z-index: 1;
    }

    .back-button {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      z-index: 2;
    }

    .back-button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-50%) translateX(-2px);
    }

    .credits-display {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 50px 20px;
      text-align: center;
      margin: 30px 20px;
      border-radius: 24px;
      box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
      animation: fadeInUp 0.8s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .credits-display::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .credits-title {
      font-size: 1.2rem;
      color: #666;
      margin-bottom: 15px;
      font-weight: 500;
    }

    .credits-amount {
      font-size: 4rem;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 10px;
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .credits-suffix {
      font-size: 1.5rem;
      color: #888;
      font-weight: 600;
    }

    .records-section {
      max-width: 1000px;
      margin: 30px auto;
      padding: 0 20px;
      animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .section-title {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 25px 35px;
      border-radius: 20px 20px 0 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e1e2f;
      box-shadow: 0 -15px 35px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .section-title::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .section-title::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 4s infinite;
    }

    .records-list {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 0 0 20px 20px;
      box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      overflow: hidden;
    }

    .record-item {
      padding: 25px 35px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
      background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.02), transparent);
      animation: slideInFromLeft 0.6s ease-out;
      animation-fill-mode: both;
    }

    .record-item:nth-child(1) { animation-delay: 0.1s; }
    .record-item:nth-child(2) { animation-delay: 0.2s; }
    .record-item:nth-child(3) { animation-delay: 0.3s; }
    .record-item:nth-child(4) { animation-delay: 0.4s; }
    .record-item:nth-child(5) { animation-delay: 0.5s; }
    .record-item:nth-child(6) { animation-delay: 0.6s; }
    .record-item:nth-child(7) { animation-delay: 0.7s; }
    .record-item:nth-child(8) { animation-delay: 0.8s; }

    @keyframes slideInFromLeft {
      from {
        opacity: 0;
        transform: translateX(-30px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .record-item::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .record-item:last-child {
      border-bottom: none;
    }

    .record-item:hover {
      background: linear-gradient(90deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.05), rgba(102, 126, 234, 0.08));
      transform: translateX(10px) translateY(-2px);
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
    }

    .record-item:hover::before {
      transform: scaleY(1);
    }

    .record-left {
      flex: 1;
    }

    .record-name {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e1e2f;
      margin-bottom: 5px;
    }

    .record-time {
      font-size: 0.9rem;
      color: #888;
    }

    .record-credits {
      font-size: 1.3rem;
      font-weight: 700;
      padding: 12px 20px;
      border-radius: 25px;
      min-width: 90px;
      text-align: center;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .record-credits::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s ease;
    }

    .record-item:hover .record-credits::before {
      left: 100%;
    }

    .credits-positive {
      color: #22c55e;
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08));
      border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .credits-positive:hover {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.12));
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    }

    .credits-negative {
      color: #ef4444;
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.08));
      border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .credits-negative:hover {
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.12));
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    }

    .floating-elements {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .floating-circle:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-circle:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .floating-circle:nth-child(3) {
      width: 40px;
      height: 40px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 2rem;
      }
      
      .back-button {
        left: 15px;
        padding: 10px 16px;
        font-size: 12px;
      }
      
      .credits-display {
        margin: 20px 15px;
        padding: 30px 20px;
      }
      
      .credits-amount {
        font-size: 3rem;
      }
      
      .records-section {
        padding: 0 15px;
      }
      
      .section-title {
        padding: 20px 25px;
        font-size: 1.3rem;
      }
      
      .record-item {
        padding: 20px 25px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
      
      .record-item:hover {
        transform: translateY(-2px);
      }
      
      .record-credits {
        align-self: flex-end;
        font-size: 1.2rem;
        padding: 10px 18px;
        min-width: 80px;
      }
    }
  </style>
</head>
<body>
  <div class="background-pattern"></div>
  <div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
  </div>

  <div class="header">
    <button class="back-button" onclick="window.location.href='AI lab-账号系统.html'">← 返回</button>
    <h1>💰 积分中心</h1>
  </div>

  <div class="credits-display">
    <div class="credits-title">当前可用积分</div>
    <div class="credits-amount">1200</div>
    <div class="credits-suffix">分</div>
  </div>

  <div class="records-section">
    <div class="section-title">📊 积分记录</div>
    <div class="records-list">
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">签到奖励</div>
          <div class="record-time">2024-01-15 09:00:00</div>
        </div>
        <div class="record-credits credits-positive">+50</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">解锁技能 - 听我说唱</div>
          <div class="record-time">2024-01-14 15:30:00</div>
        </div>
        <div class="record-credits credits-negative">-200</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">完成任务 - 语音训练</div>
          <div class="record-time">2024-01-14 14:20:00</div>
        </div>
        <div class="record-credits credits-positive">+100</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">邀请好友奖励</div>
          <div class="record-time">2024-01-13 20:45:00</div>
        </div>
        <div class="record-credits credits-positive">+300</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">解锁技能 - 大学问家</div>
          <div class="record-time">2024-01-12 16:15:00</div>
        </div>
        <div class="record-credits credits-negative">-500</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">首次登录奖励</div>
          <div class="record-time">2024-01-10 10:00:00</div>
        </div>
        <div class="record-credits credits-positive">+1000</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">每日活跃奖励</div>
          <div class="record-time">2024-01-09 18:30:00</div>
        </div>
        <div class="record-credits credits-positive">+20</div>
      </div>
      
      <div class="record-item">
        <div class="record-left">
          <div class="record-name">解锁技能 - 智能助手</div>
          <div class="record-time">2024-01-08 11:45:00</div>
        </div>
        <div class="record-credits credits-negative">-350</div>
      </div>
    </div>
  </div>
</body>
</html> 