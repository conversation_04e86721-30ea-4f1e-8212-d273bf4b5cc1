// 本地存储管理系统
class GameStorage {
    constructor() {
        this.storageKey = 'battlecity_game_data';
        this.defaultData = {
            highScore: 0,
            totalKills: 0,
            totalGamesPlayed: 0,
            totalPlayTime: 0,
            currentLevel: 1,
            playerSkin: 'classic',
            difficulty: 'normal',
            soundEnabled: true,
            musicEnabled: true,
            achievements: {},
            statistics: {
                bulletsShot: 0,
                bulletsHit: 0,
                powerupsCollected: 0,
                levelsCompleted: 0,
                basesSaved: 0,
                perfectLevels: 0
            },
            unlockedSkins: ['classic'],
            settings: {
                volume: 0.7,
                musicVolume: 0.5,
                effectsVolume: 0.8,
                showMinimap: true,
                showParticles: true
            }
        };
        this.data = this.loadData();
    }

    // 加载数据
    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const parsed = JSON.parse(stored);
                // 合并默认数据和存储数据，确保新字段存在
                return this.mergeData(this.defaultData, parsed);
            }
        } catch (error) {
            console.warn('Failed to load game data:', error);
        }
        return { ...this.defaultData };
    }

    // 合并数据对象
    mergeData(defaultObj, storedObj) {
        const result = { ...defaultObj };
        
        for (const key in storedObj) {
            if (typeof defaultObj[key] === 'object' && defaultObj[key] !== null && !Array.isArray(defaultObj[key])) {
                result[key] = this.mergeData(defaultObj[key], storedObj[key]);
            } else {
                result[key] = storedObj[key];
            }
        }
        
        return result;
    }

    // 保存数据
    saveData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
            return true;
        } catch (error) {
            console.error('Failed to save game data:', error);
            return false;
        }
    }

    // 获取数据
    get(key) {
        const keys = key.split('.');
        let value = this.data;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return undefined;
            }
        }
        
        return value;
    }

    // 设置数据
    set(key, value) {
        const keys = key.split('.');
        let current = this.data;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in current) || typeof current[k] !== 'object') {
                current[k] = {};
            }
            current = current[k];
        }
        
        current[keys[keys.length - 1]] = value;
        this.saveData();
    }

    // 增加数值
    increment(key, amount = 1) {
        const current = this.get(key) || 0;
        this.set(key, current + amount);
    }

    // 更新最高分
    updateHighScore(score) {
        if (score > this.data.highScore) {
            this.data.highScore = score;
            this.saveData();
            return true; // 新纪录
        }
        return false;
    }

    // 解锁成就
    unlockAchievement(achievementId) {
        if (!this.data.achievements[achievementId]) {
            this.data.achievements[achievementId] = {
                unlocked: true,
                unlockedAt: Date.now()
            };
            this.saveData();
            return true; // 新成就
        }
        return false;
    }

    // 检查成就是否已解锁
    isAchievementUnlocked(achievementId) {
        return this.data.achievements[achievementId]?.unlocked || false;
    }

    // 解锁皮肤
    unlockSkin(skinId) {
        if (!this.data.unlockedSkins.includes(skinId)) {
            this.data.unlockedSkins.push(skinId);
            this.saveData();
            return true; // 新皮肤
        }
        return false;
    }

    // 检查皮肤是否已解锁
    isSkinUnlocked(skinId) {
        return this.data.unlockedSkins.includes(skinId);
    }

    // 获取游戏统计
    getStatistics() {
        return { ...this.data.statistics };
    }

    // 更新统计数据
    updateStatistic(key, value) {
        if (key in this.data.statistics) {
            this.data.statistics[key] = value;
            this.saveData();
        }
    }

    // 增加统计数据
    incrementStatistic(key, amount = 1) {
        if (key in this.data.statistics) {
            this.data.statistics[key] += amount;
            this.saveData();
        }
    }

    // 重置游戏数据
    resetData() {
        this.data = { ...this.defaultData };
        this.saveData();
    }

    // 导出数据
    exportData() {
        return JSON.stringify(this.data, null, 2);
    }

    // 导入数据
    importData(jsonString) {
        try {
            const imported = JSON.parse(jsonString);
            this.data = this.mergeData(this.defaultData, imported);
            this.saveData();
            return true;
        } catch (error) {
            console.error('Failed to import data:', error);
            return false;
        }
    }

    // 获取游戏设置
    getSettings() {
        return { ...this.data.settings };
    }

    // 更新设置
    updateSetting(key, value) {
        if (key in this.data.settings) {
            this.data.settings[key] = value;
            this.saveData();
        }
    }

    // 获取已解锁的皮肤列表
    getUnlockedSkins() {
        return [...this.data.unlockedSkins];
    }

    // 获取所有成就状态
    getAchievements() {
        return { ...this.data.achievements };
    }

    // 计算准确率
    getAccuracy() {
        const shot = this.data.statistics.bulletsShot;
        const hit = this.data.statistics.bulletsHit;
        return shot > 0 ? Math.round((hit / shot) * 100) : 0;
    }

    // 获取游戏总时长（格式化）
    getFormattedPlayTime() {
        const totalMinutes = Math.floor(this.data.totalPlayTime / 60);
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }

    // 记录游戏会话
    recordGameSession(duration, score, level, kills) {
        this.increment('totalGamesPlayed');
        this.increment('totalPlayTime', duration);
        this.increment('totalKills', kills);
        this.updateHighScore(score);
        
        // 更新当前关卡记录
        if (level > this.data.currentLevel) {
            this.set('currentLevel', level);
        }
        
        this.saveData();
    }
}

// 创建全局存储实例
window.gameStorage = new GameStorage();
