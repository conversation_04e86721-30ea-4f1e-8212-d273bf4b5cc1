<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Lab 我的技能</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      overflow-x: hidden;
    }

    .background-pattern {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
      z-index: -1;
    }

    .header {
      background: rgba(30, 30, 47, 0.95);
      backdrop-filter: blur(20px);
      color: white;
      padding: 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, #fff, #e0e7ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      z-index: 1;
    }

    .back-button {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      z-index: 2;
    }

    .back-button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-50%) translateX(-2px);
    }

    .skills-container {
      max-width: 1200px;
      margin: 30px auto;
      padding: 0 20px;
    }

    .skills-section {
      margin-bottom: 40px;
      animation: fadeInUp 0.8s ease-out;
      animation-fill-mode: both;
    }

    .skills-section:nth-child(2) { animation-delay: 0.2s; }
    .skills-section:nth-child(3) { animation-delay: 0.4s; }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .section-title {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      padding: 25px 35px;
      border-radius: 20px 20px 0 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e1e2f;
      box-shadow: 0 -15px 35px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .section-title::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .section-title::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shimmer 4s infinite;
    }

    .skills-grid {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 0 0 20px 20px;
      box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      padding: 30px;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 25px;
    }

    .skill-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 245, 255, 0.9));
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 25px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
    }

    .skill-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .skill-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    }

    .skill-card:hover::before {
      transform: scaleX(1);
    }

    .skill-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-bottom: 15px;
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }

    .skill-name {
      font-size: 1.3rem;
      font-weight: 600;
      color: #1e1e2f;
      margin-bottom: 8px;
    }

    .skill-description {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .skill-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .skill-status {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .status-active {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.08));
      color: #22c55e;
      border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .status-using {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.08));
      color: #3b82f6;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .skill-usage {
      font-size: 0.8rem;
      color: #888;
    }

    .skill-price {
      font-size: 1.1rem;
      font-weight: 600;
      color: #667eea;
    }

    .unlock-button {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 12px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .unlock-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    .unlock-button:hover::before {
      left: 100%;
    }

    .button-enabled {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    .button-enabled:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    }

    .button-disabled {
      background: linear-gradient(135deg, rgba(156, 163, 175, 0.5), rgba(156, 163, 175, 0.3));
      color: #9ca3af;
      cursor: not-allowed;
    }

    .floating-elements {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .floating-circle:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-circle:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .floating-circle:nth-child(3) {
      width: 40px;
      height: 40px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 2rem;
      }
      
      .back-button {
        left: 15px;
        padding: 10px 16px;
        font-size: 12px;
      }
      
      .skills-container {
        padding: 0 15px;
      }
      
      .section-title {
        padding: 20px 25px;
        font-size: 1.3rem;
      }
      
      .skills-grid {
        padding: 20px;
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .skill-card {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="background-pattern"></div>
  <div class="floating-elements">
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
    <div class="floating-circle"></div>
  </div>

  <div class="header">
    <button class="back-button" onclick="window.location.href='AI lab-账号系统.html'">← 返回</button>
    <h1>⚡ 我的技能</h1>
  </div>

  <div class="skills-container">
    <!-- 已解锁技能 -->
    <div class="skills-section">
      <div class="section-title">🎯 已解锁技能</div>
      <div class="skills-grid">
        <div class="skill-card">
          <div class="skill-icon">🎤</div>
          <div class="skill-name">听我说唱</div>
          <div class="skill-description">AI智能说唱创作，支持多种风格的说唱歌词生成和节拍匹配</div>
          <div class="skill-meta">
            <span class="skill-status status-using">使用中</span>
            <span class="skill-usage">今日使用 3 次</span>
          </div>
        </div>

        <div class="skill-card">
          <div class="skill-icon">🎓</div>
          <div class="skill-name">大学问家</div>
          <div class="skill-description">专业知识问答系统，涵盖多个学科领域的深度解答</div>
          <div class="skill-meta">
            <span class="skill-status status-active">已激活</span>
            <span class="skill-usage">上次使用 2 小时前</span>
          </div>
        </div>

        <div class="skill-card">
          <div class="skill-icon">🤖</div>
          <div class="skill-name">智能助手</div>
          <div class="skill-description">全能AI助手，支持日程管理、提醒设置和智能对话</div>
          <div class="skill-meta">
            <span class="skill-status status-active">已激活</span>
            <span class="skill-usage">昨天使用 12 次</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 待解锁技能 -->
    <div class="skills-section">
      <div class="section-title">🔒 待解锁技能</div>
      <div class="skills-grid">
        <div class="skill-card">
          <div class="skill-icon">🎨</div>
          <div class="skill-name">AI画师</div>
          <div class="skill-description">AI图像生成和编辑，支持多种艺术风格的创作</div>
          <div class="skill-meta">
            <span class="skill-price">需 500 积分</span>
          </div>
          <button class="unlock-button button-enabled" onclick="unlockSkill('ai-painter', 500)">
            立即解锁
          </button>
        </div>

        <div class="skill-card">
          <div class="skill-icon">📝</div>
          <div class="skill-name">文案大师</div>
          <div class="skill-description">专业文案创作，包括广告文案、营销内容和创意写作</div>
          <div class="skill-meta">
            <span class="skill-price">需 300 积分</span>
          </div>
          <button class="unlock-button button-enabled" onclick="unlockSkill('copywriter', 300)">
            立即解锁
          </button>
        </div>

        <div class="skill-card">
          <div class="skill-icon">📊</div>
          <div class="skill-name">数据分析师</div>
          <div class="skill-description">智能数据分析和可视化，提供深度洞察和预测分析</div>
          <div class="skill-meta">
            <span class="skill-price">需 800 积分</span>
          </div>
          <button class="unlock-button button-enabled" onclick="unlockSkill('data-analyst', 800)">
            立即解锁
          </button>
        </div>

        <div class="skill-card">
          <div class="skill-icon">🎵</div>
          <div class="skill-name">音乐制作人</div>
          <div class="skill-description">AI音乐创作和编曲，支持多种乐器和音乐风格</div>
          <div class="skill-meta">
            <span class="skill-price">需 1500 积分</span>
          </div>
          <button class="unlock-button button-disabled" onclick="unlockSkill('music-producer', 1500)">
            积分不足
          </button>
        </div>

        <div class="skill-card">
          <div class="skill-icon">🏗️</div>
          <div class="skill-name">架构师</div>
          <div class="skill-description">系统架构设计和技术方案规划，提供专业的技术咨询</div>
          <div class="skill-meta">
            <span class="skill-price">需 2000 积分</span>
          </div>
          <button class="unlock-button button-disabled" onclick="unlockSkill('architect', 2000)">
            积分不足
          </button>
        </div>

        <div class="skill-card">
          <div class="skill-icon">🌍</div>
          <div class="skill-name">多语言翻译</div>
          <div class="skill-description">专业多语言翻译服务，支持100+语言的精准翻译</div>
          <div class="skill-meta">
            <span class="skill-price">需 600 积分</span>
          </div>
          <button class="unlock-button button-enabled" onclick="unlockSkill('translator', 600)">
            立即解锁
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 当前用户积分
    const currentCredits = 1200;

    // 解锁技能函数
    function unlockSkill(skillId, requiredCredits) {
      if (currentCredits >= requiredCredits) {
        // 模拟解锁过程
        const confirmUnlock = confirm(`确认花费 ${requiredCredits} 积分解锁该技能吗？\n当前积分: ${currentCredits}`);
        if (confirmUnlock) {
          alert('技能解锁成功！正在跳转到积分中心查看详情...');
          // 这里可以添加实际的解锁逻辑
          // 然后跳转到积分中心
          setTimeout(() => {
            window.location.href = 'credits-center.html';
          }, 1500);
        }
      } else {
        alert(`积分不足！需要 ${requiredCredits} 积分，当前只有 ${currentCredits} 积分。`);
      }
    }

    // 页面加载时根据积分更新按钮状态
    document.addEventListener('DOMContentLoaded', function() {
      const buttons = document.querySelectorAll('.unlock-button');
      buttons.forEach(button => {
        const skillCard = button.closest('.skill-card');
        const priceText = skillCard.querySelector('.skill-price').textContent;
        const requiredCredits = parseInt(priceText.match(/\d+/)[0]);
        
        if (currentCredits >= requiredCredits) {
          button.classList.remove('button-disabled');
          button.classList.add('button-enabled');
          button.textContent = '立即解锁';
          button.style.cursor = 'pointer';
        } else {
          button.classList.remove('button-enabled');
          button.classList.add('button-disabled');
          button.textContent = '积分不足';
          button.style.cursor = 'not-allowed';
        }
      });
    });
  </script>
</body>
</html> 