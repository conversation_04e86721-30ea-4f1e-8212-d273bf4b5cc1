<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇 (Snake)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #282c34; color: #abb2bf; text-align: center; }
        h1 { color: #61afef; }
        canvas { background-color: #21252b; display: block; margin: 20px auto; border: 2px solid #61afef; }
        p { font-size: 1.2em; }
    </style>
</head>
<body>
    <h1>贪吃蛇 (Snake) - 敬请期待</h1>
    <p>这里将是贪吃蛇游戏的展示区域。</p>
    <canvas id="gameCanvas" width="400" height="400">
        您的浏览器不支持Canvas。
    </canvas>
    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#61afef';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Snake Game Area', canvas.width / 2, canvas.height / 2);
    </script>
</body>
</html> 