// 游戏引擎核心
class GameEngine {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.minimapCanvas = null;
        this.minimapCtx = null;

        // 游戏状态
        this.gameState = 'menu'; // menu, playing, paused, gameOver, levelComplete
        this.currentLevel = 1;
        this.score = 0;
        this.lives = 3;
        this.gameTime = 0;
        this.levelStartTime = 0;

        // 游戏实体
        this.player = null;
        this.enemies = [];
        this.bullets = [];
        this.walls = [];
        this.base = null;

        // 游戏配置
        this.maxEnemiesOnScreen = 4;
        this.enemySpawnQueue = [];
        this.enemySpawnTimer = 0;
        this.enemySpawnInterval = 3000; // 3秒

        // 输入处理
        this.keys = {};
        this.lastKeyTime = {};

        // 统计数据
        this.stats = {
            enemiesKilled: 0,
            bulletsFired: 0,
            bulletsHit: 0,
            powerupsCollected: 0,
            levelStartTime: 0
        };

        // 游戏循环
        this.lastTime = 0;
        this.animationId = null;
        this.isRunning = false;

        // 难度设置
        this.difficulty = 'normal';
        this.difficultyMultipliers = {
            easy: { enemySpeed: 0.7, enemyCount: 0.8, playerHealth: 1.5 },
            normal: { enemySpeed: 1.0, enemyCount: 1.0, playerHealth: 1.0 },
            hard: { enemySpeed: 1.3, enemyCount: 1.2, playerHealth: 0.7 }
        };

        this.init();
    }

    // 初始化游戏引擎
    init() {
        console.log('=== GameEngine.init() called ===');
        console.log('Initializing game engine...');

        this.canvas = document.getElementById('gameCanvas');
        this.minimapCanvas = document.getElementById('minimapCanvas');

        console.log('Canvas element:', this.canvas);
        console.log('Minimap canvas element:', this.minimapCanvas);

        if (!this.canvas) {
            console.error('Game canvas not found!');
            console.log('Available elements with id containing "canvas":');
            document.querySelectorAll('[id*="canvas"]').forEach(el => {
                console.log(`- ${el.id}:`, el);
            });
            return;
        }

        console.log('Canvas dimensions:', this.canvas.width, 'x', this.canvas.height);
        console.log('Canvas style:', this.canvas.style.cssText);

        this.ctx = this.canvas.getContext('2d');
        if (this.minimapCanvas) {
            this.minimapCtx = this.minimapCanvas.getContext('2d');
        }

        console.log('Canvas context:', this.ctx);
        console.log('Minimap context:', this.minimapCtx);

        if (!this.ctx) {
            console.error('Failed to get 2D context from canvas!');
            return;
        }

        // 设置画布属性
        this.ctx.imageSmoothingEnabled = false;

        console.log('Canvas context initialized successfully');

        // 测试绘制
        try {
            this.ctx.fillStyle = 'red';
            this.ctx.fillRect(10, 10, 50, 50);
            console.log('Test draw completed successfully');
        } catch (error) {
            console.error('Test draw failed:', error);
        }

        // 绑定事件
        this.bindEvents();

        // 加载设置
        this.loadSettings();

        console.log('Game engine initialized successfully');
        console.log('=== GameEngine.init() completed ===');
    }

    // 绑定事件
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // 防止方向键滚动页面
        document.addEventListener('keydown', (e) => {
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space'].includes(e.code)) {
                e.preventDefault();
            }
        });

        // 窗口失焦时暂停游戏
        window.addEventListener('blur', () => {
            if (this.gameState === 'playing') {
                this.pauseGame();
            }
        });
    }

    // 加载设置
    loadSettings() {
        if (window.gameStorage) {
            this.difficulty = window.gameStorage.get('difficulty') || 'normal';
            this.currentLevel = window.gameStorage.get('currentLevel') || 1;
        }
    }

    // 开始新游戏
    startNewGame(difficulty = 'normal') {
        console.log('=== GameEngine.startNewGame() called ===');
        console.log(`Starting new game with difficulty: ${difficulty}`);

        this.difficulty = difficulty;
        this.currentLevel = 1;
        this.score = 0;
        this.lives = Math.floor(3 * this.difficultyMultipliers[difficulty].playerHealth);
        this.gameTime = 0;

        console.log(`Game initialized - Lives: ${this.lives}, Level: ${this.currentLevel}`);

        // 重置统计
        this.stats = {
            enemiesKilled: 0,
            bulletsFired: 0,
            bulletsHit: 0,
            powerupsCollected: 0,
            levelStartTime: Date.now()
        };

        console.log('Starting level 1...');
        try {
            this.startLevel(this.currentLevel);
            console.log('=== GameEngine.startNewGame() completed successfully ===');
        } catch (error) {
            console.error('Error in startNewGame():', error);
            console.error('Error stack:', error.stack);
        }
    }

    // 开始关卡
    startLevel(level) {
        console.log(`Starting level ${level}...`);

        this.currentLevel = level;
        this.levelStartTime = Date.now();
        this.stats.levelStartTime = this.levelStartTime;

        // 生成地图
        console.log('Generating map...');
        const mapData = window.mapGenerator.generateLevel(level);
        console.log('Map data:', mapData);

        console.log('Loading map...');
        this.loadMap(mapData);

        // 生成敌人队列
        console.log('Generating enemy config...');
        const enemyConfig = window.mapGenerator.generateEnemyConfig(level);
        console.log('Enemy config:', enemyConfig);

        this.generateEnemyQueue(enemyConfig);

        // 重置道具系统
        if (window.powerupManager) {
            window.powerupManager.clear();
        }

        // 设置游戏状态
        this.gameState = 'playing';
        this.isRunning = true;

        console.log('Starting game loop...');
        // 开始游戏循环
        this.start();

        // 播放背景音乐
        if (window.audioManager) {
            window.audioManager.startBackgroundMusic();
        }

        // 更新UI
        this.updateUI();

        console.log(`Level ${level} started successfully`);
    }

    // 加载地图
    loadMap(mapData) {
        console.log('Loading map with data:', mapData);

        // 清空现有实体
        this.enemies = [];
        this.bullets = [];
        this.walls = [];

        // 创建玩家
        console.log('Creating player at:', mapData.playerSpawn);
        this.player = new Tank(mapData.playerSpawn.x, mapData.playerSpawn.y, 'player');
        console.log('Player created:', this.player);

        // 创建墙壁
        console.log('Creating walls, count:', mapData.walls.length);
        mapData.walls.forEach(wallData => {
            this.walls.push(new Wall(wallData.x, wallData.y, wallData.type));
        });
        console.log('Walls created, total:', this.walls.length);

        // 创建基地
        console.log('Creating base at:', mapData.base);
        this.base = new Base(mapData.base.x, mapData.base.y);
        console.log('Base created:', this.base);

        // 保存敌人出生点
        this.enemySpawns = mapData.enemySpawns;
        console.log('Enemy spawns:', this.enemySpawns);

        console.log('Map loaded successfully');
    }

    // 生成敌人队列
    generateEnemyQueue(enemyConfig) {
        this.enemySpawnQueue = [];

        enemyConfig.types.forEach(typeConfig => {
            for (let i = 0; i < typeConfig.count; i++) {
                this.enemySpawnQueue.push({
                    type: typeConfig.type,
                    health: typeConfig.health,
                    speed: typeConfig.speed * this.difficultyMultipliers[this.difficulty].enemySpeed
                });
            }
        });

        // 打乱队列
        this.shuffleArray(this.enemySpawnQueue);

        this.enemySpawnTimer = 0;
    }

    // 打乱数组
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    // 开始游戏循环
    start() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.lastTime = performance.now();
        this.gameLoop();
    }

    // 停止游戏循环
    stop() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    // 游戏主循环
    gameLoop(currentTime = performance.now()) {
        if (!this.isRunning) return;

        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        // 更新游戏
        this.update(deltaTime);

        // 渲染游戏
        this.render();

        // 继续循环
        this.animationId = requestAnimationFrame((time) => this.gameLoop(time));
    }

    // 更新游戏逻辑
    update(deltaTime) {
        if (this.gameState !== 'playing') return;

        // 更新游戏时间
        this.gameTime += deltaTime;

        // 处理输入
        this.handleInput();

        // 生成敌人
        this.spawnEnemies(deltaTime);

        // 更新玩家
        if (this.player && this.player.active) {
            this.player.update();
        }

        // 更新敌人
        this.enemies.forEach(enemy => {
            if (enemy.active) {
                enemy.update();

                // 处理冰冻效果
                if (window.powerupManager && window.powerupManager.hasPowerup('freeze')) {
                    // 敌人被冰冻，跳过移动
                    return;
                }
            }
        });

        // 更新子弹
        this.bullets.forEach(bullet => {
            if (bullet.active) {
                bullet.update();
            }
        });

        // 更新道具系统
        if (window.powerupManager) {
            window.powerupManager.update(deltaTime);

            // 检查炸弹效果
            if (window.powerupManager.consumeBombEffect()) {
                this.destroyAllEnemies();
            }
        }

        // 碰撞检测
        this.handleCollisions();

        // 清理非活跃实体
        this.cleanupEntities();

        // 检查游戏状态
        this.checkGameState();

        // 更新UI
        this.updateUI();
    }

    // 处理输入
    handleInput() {
        if (!this.player || !this.player.active) return;

        const now = Date.now();

        // 移动控制
        if (this.keys['KeyW'] || this.keys['ArrowUp']) {
            this.player.move(0);
        } else if (this.keys['KeyS'] || this.keys['ArrowDown']) {
            this.player.move(2);
        } else if (this.keys['KeyA'] || this.keys['ArrowLeft']) {
            this.player.move(3);
        } else if (this.keys['KeyD'] || this.keys['ArrowRight']) {
            this.player.move(1);
        }

        // 射击控制
        if (this.keys['Space'] && now - (this.lastKeyTime['Space'] || 0) > 100) {
            const bullets = this.player.shoot();
            if (bullets) {
                bullets.forEach(bullet => this.bullets.push(bullet));
                this.stats.bulletsFired += bullets.length;

                // 更新统计
                if (window.gameStorage) {
                    window.gameStorage.incrementStatistic('bulletsShot', bullets.length);
                }
            }
            this.lastKeyTime['Space'] = now;
        }

        // 暂停控制
        if (this.keys['KeyP'] && now - (this.lastKeyTime['KeyP'] || 0) > 300) {
            this.pauseGame();
            this.lastKeyTime['KeyP'] = now;
        }
    }

    // 生成敌人
    spawnEnemies(deltaTime) {
        if (this.enemySpawnQueue.length === 0) return;
        if (this.enemies.filter(e => e.active).length >= this.maxEnemiesOnScreen) return;

        this.enemySpawnTimer += deltaTime;
        if (this.enemySpawnTimer >= this.enemySpawnInterval) {
            const enemyConfig = this.enemySpawnQueue.shift();
            const spawnPoint = this.enemySpawns[Math.floor(Math.random() * this.enemySpawns.length)];

            const enemy = new Tank(spawnPoint.x, spawnPoint.y, 'enemy');
            enemy.health = enemyConfig.health;
            enemy.maxHealth = enemyConfig.health;
            enemy.speed = enemyConfig.speed;

            this.enemies.push(enemy);
            this.enemySpawnTimer = 0;

            // 创建生成效果
            if (window.effectsManager) {
                window.effectsManager.createPowerupEffect(spawnPoint.x + 16, spawnPoint.y + 16);
            }
        }
    }

    // 碰撞检测
    handleCollisions() {
        // 子弹与墙壁碰撞
        this.bullets.forEach(bullet => {
            if (!bullet.active) return;

            this.walls.forEach(wall => {
                if (!wall.active) return;

                if (bullet.collidesWith(wall)) {
                    bullet.hit();

                    if (wall.type !== 'grass' && wall.type !== 'water') {
                        if (wall.takeDamage(bullet.damage)) {
                            this.addScore(10); // 摧毁墙壁得分
                        }
                    }
                }
            });
        });

        // 子弹与坦克碰撞
        this.bullets.forEach(bullet => {
            if (!bullet.active) return;

            // 玩家子弹击中敌人
            if (bullet.owner === 'player') {
                this.enemies.forEach(enemy => {
                    if (!enemy.active) return;

                    if (bullet.collidesWith(enemy)) {
                        bullet.hit();
                        this.stats.bulletsHit++;

                        if (enemy.takeDamage(bullet.damage)) {
                            this.enemyDestroyed(enemy);
                        }
                    }
                });
            }

            // 敌人子弹击中玩家
            if (bullet.owner === 'enemy' && this.player && this.player.active) {
                if (bullet.collidesWith(this.player)) {
                    bullet.hit();

                    // 检查护盾
                    if (this.player.shield) {
                        this.player.shield = false;
                        if (window.powerupManager) {
                            window.powerupManager.deactivatePowerup('shield');
                        }
                    } else {
                        if (this.player.takeDamage(bullet.damage)) {
                            this.playerDestroyed();
                        }
                    }
                }
            }
        });

        // 子弹与基地碰撞
        this.bullets.forEach(bullet => {
            if (!bullet.active || !this.base || !this.base.active) return;

            if (bullet.collidesWith(this.base)) {
                bullet.hit();
                if (this.base.takeDamage(bullet.damage)) {
                    this.baseDestroyed();
                }
            }
        });

        // 子弹与子弹碰撞
        for (let i = 0; i < this.bullets.length; i++) {
            const bullet1 = this.bullets[i];
            if (!bullet1.active) continue;

            for (let j = i + 1; j < this.bullets.length; j++) {
                const bullet2 = this.bullets[j];
                if (!bullet2.active) continue;

                if (bullet1.owner !== bullet2.owner && bullet1.collidesWith(bullet2)) {
                    bullet1.hit();
                    bullet2.hit();
                }
            }
        }

        // 坦克与墙壁碰撞
        const allTanks = [this.player, ...this.enemies].filter(tank => tank && tank.active);
        allTanks.forEach(tank => {
            this.walls.forEach(wall => {
                if (!wall.active) return;

                if (wall.type === 'grass') return; // 草地可以通过

                if (tank.collidesWith(wall)) {
                    // 简单的碰撞回退
                    const overlap = this.getOverlap(tank, wall);
                    if (overlap.x > overlap.y) {
                        tank.x -= overlap.x * (tank.x < wall.x ? -1 : 1);
                    } else {
                        tank.y -= overlap.y * (tank.y < wall.y ? -1 : 1);
                    }
                }
            });
        });

        // 玩家与道具碰撞
        if (this.player && this.player.active && window.powerupManager) {
            const powerups = window.powerupManager.getPowerups();
            powerups.forEach(powerup => {
                if (this.player.collidesWith(powerup)) {
                    if (window.powerupManager.collectPowerup(powerup, this.player)) {
                        this.stats.powerupsCollected++;
                        this.addScore(100); // 收集道具得分
                    }
                }
            });
        }
    }

    // 获取重叠区域
    getOverlap(rect1, rect2) {
        const bounds1 = rect1.getBounds();
        const bounds2 = rect2.getBounds();

        const overlapX = Math.min(bounds1.right, bounds2.right) - Math.max(bounds1.left, bounds2.left);
        const overlapY = Math.min(bounds1.bottom, bounds2.bottom) - Math.max(bounds1.top, bounds2.top);

        return { x: Math.max(0, overlapX), y: Math.max(0, overlapY) };
    }

    // 敌人被摧毁
    enemyDestroyed(enemy) {
        this.stats.enemiesKilled++;
        this.addScore(100 + (this.currentLevel - 1) * 10);

        // 更新统计
        if (window.gameStorage) {
            window.gameStorage.incrementStatistic('bulletsHit');
            window.gameStorage.increment('totalKills');
        }

        // 创建文字效果
        if (window.effectsManager) {
            const center = enemy.getCenter();
            window.effectsManager.createTextEffect(center.x, center.y - 20, '+100', '#ffd23f', 16);
        }
    }

    // 玩家被摧毁
    playerDestroyed() {
        this.lives--;

        if (this.lives > 0) {
            // 重生玩家
            setTimeout(() => {
                if (this.gameState === 'playing') {
                    const mapData = window.mapGenerator.generateLevel(this.currentLevel);
                    this.player = new Tank(mapData.playerSpawn.x, mapData.playerSpawn.y, 'player');
                }
            }, 2000);
        } else {
            // 游戏结束
            this.gameOver();
        }
    }

    // 基地被摧毁
    baseDestroyed() {
        this.gameOver();
    }

    // 摧毁所有敌人（炸弹效果）
    destroyAllEnemies() {
        this.enemies.forEach(enemy => {
            if (enemy.active) {
                enemy.active = false;
                this.enemyDestroyed(enemy);

                // 创建爆炸效果
                if (window.effectsManager) {
                    const center = enemy.getCenter();
                    window.effectsManager.createExplosion(center.x, center.y, 1.5);
                }
            }
        });

        // 播放复合音效
        if (window.audioManager) {
            window.audioManager.playComplexSound('victory');
        }
    }

    // 清理非活跃实体
    cleanupEntities() {
        this.enemies = this.enemies.filter(enemy => enemy.active);
        this.bullets = this.bullets.filter(bullet => bullet.active);
        this.walls = this.walls.filter(wall => wall.active);
    }

    // 检查游戏状态
    checkGameState() {
        // 检查关卡完成
        if (this.enemySpawnQueue.length === 0 && this.enemies.filter(e => e.active).length === 0) {
            this.levelComplete();
        }
    }

    // 关卡完成
    levelComplete() {
        this.gameState = 'levelComplete';
        this.stop();

        // 计算奖励
        const levelTime = Date.now() - this.levelStartTime;
        const timeBonus = Math.max(0, 30000 - levelTime) / 100; // 时间奖励
        const levelBonus = this.currentLevel * 1000; // 关卡奖励

        this.addScore(levelBonus + timeBonus);

        // 播放关卡完成音效
        if (window.audioManager) {
            window.audioManager.playLevelComplete();
        }

        // 创建庆祝效果
        if (window.effectsManager) {
            window.effectsManager.createLevelCompleteEffect();
        }

        // 更新统计
        if (window.gameStorage) {
            window.gameStorage.incrementStatistic('levelsCompleted');
            window.gameStorage.set('currentLevel', this.currentLevel + 1);
        }

        // 检查成就
        this.checkAchievements();

        console.log(`Level ${this.currentLevel} completed!`);
    }

    // 游戏结束
    gameOver() {
        this.gameState = 'gameOver';
        this.stop();

        // 停止背景音乐
        if (window.audioManager) {
            window.audioManager.stopBackgroundMusic();
        }

        // 记录游戏会话
        const sessionTime = Math.floor(this.gameTime / 1000);
        if (window.gameStorage) {
            window.gameStorage.recordGameSession(
                sessionTime,
                this.score,
                this.currentLevel,
                this.stats.enemiesKilled
            );
        }

        // 检查成就
        this.checkAchievements();

        console.log('Game Over');
    }

    // 暂停游戏
    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            this.stop();
        }
    }

    // 恢复游戏
    resumeGame() {
        if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.start();
        }
    }

    // 重新开始游戏
    restartGame() {
        this.stop();
        this.startNewGame(this.difficulty);
    }

    // 下一关
    nextLevel() {
        this.currentLevel++;
        this.startLevel(this.currentLevel);
    }

    // 添加分数
    addScore(points) {
        this.score += points;
    }

    // 检查成就
    checkAchievements() {
        if (!window.gameStorage) return;

        const achievements = [
            {
                id: 'first_kill',
                condition: () => this.stats.enemiesKilled >= 1,
                name: '首次击杀',
                description: '摧毁第一个敌人坦克'
            },
            {
                id: 'sharpshooter',
                condition: () => window.gameStorage.getAccuracy() >= 80,
                name: '神枪手',
                description: '命中率达到80%'
            },
            {
                id: 'survivor',
                condition: () => this.currentLevel >= 5,
                name: '幸存者',
                description: '通过5个关卡'
            },
            {
                id: 'tank_destroyer',
                condition: () => window.gameStorage.get('totalKills') >= 100,
                name: '坦克杀手',
                description: '累计摧毁100个敌人'
            },
            {
                id: 'powerup_collector',
                condition: () => this.stats.powerupsCollected >= 10,
                name: '道具收集家',
                description: '在一局游戏中收集10个道具'
            }
        ];

        achievements.forEach(achievement => {
            if (achievement.condition() && !window.gameStorage.isAchievementUnlocked(achievement.id)) {
                window.gameStorage.unlockAchievement(achievement.id);

                // 显示成就通知
                if (window.effectsManager) {
                    window.effectsManager.createTextEffect(
                        this.canvas.width / 2,
                        100,
                        `成就解锁: ${achievement.name}`,
                        '#ffd23f',
                        20
                    );
                }
            }
        });
    }

    // 渲染游戏
    render() {
        if (!this.ctx) {
            console.warn('No canvas context available for rendering');
            return;
        }

        // 清空画布
        this.ctx.fillStyle = '#2c3e50';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 调试信息（只在前几帧显示）
        if (this.gameTime < 1000) {
            console.log('Rendering frame - Walls:', this.walls.length, 'Player:', !!this.player, 'Base:', !!this.base);
        }

        // 渲染网格（可选）
        this.renderGrid();

        // 渲染墙壁
        this.walls.forEach(wall => wall.render(this.ctx));

        // 渲染基地
        if (this.base && this.base.active) {
            this.base.render(this.ctx);
        }

        // 渲染道具
        if (window.powerupManager) {
            window.powerupManager.render(this.ctx);
        }

        // 渲染坦克
        if (this.player && this.player.active) {
            this.player.render(this.ctx);
        }

        this.enemies.forEach(enemy => {
            if (enemy.active) {
                enemy.render(this.ctx);
            }
        });

        // 渲染子弹
        this.bullets.forEach(bullet => {
            if (bullet.active) {
                bullet.render(this.ctx);
            }
        });

        // 渲染冰冻效果
        if (window.powerupManager && window.powerupManager.hasPowerup('freeze')) {
            this.renderFreezeEffect();
        }

        // 渲染小地图
        this.renderMinimap();
    }

    // 渲染网格
    renderGrid() {
        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        // 垂直线
        for (let x = 0; x <= this.canvas.width; x += 32) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        // 水平线
        for (let y = 0; y <= this.canvas.height; y += 32) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }

        this.ctx.restore();
    }

    // 渲染冰冻效果
    renderFreezeEffect() {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(173, 216, 230, 0.3)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 冰晶效果
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        for (let i = 0; i < 20; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const size = 2 + Math.random() * 4;

            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }

        this.ctx.restore();
    }

    // 渲染小地图
    renderMinimap() {
        if (!this.minimapCtx) return;

        const scale = 150 / 832; // 小地图缩放比例

        // 清空小地图
        this.minimapCtx.fillStyle = '#2c3e50';
        this.minimapCtx.fillRect(0, 0, 150, 150);

        // 渲染墙壁
        this.minimapCtx.fillStyle = '#8B4513';
        this.walls.forEach(wall => {
            if (wall.active && wall.type === 'brick') {
                this.minimapCtx.fillRect(
                    wall.x * scale,
                    wall.y * scale,
                    wall.width * scale,
                    wall.height * scale
                );
            }
        });

        // 渲染钢墙
        this.minimapCtx.fillStyle = '#708090';
        this.walls.forEach(wall => {
            if (wall.active && wall.type === 'steel') {
                this.minimapCtx.fillRect(
                    wall.x * scale,
                    wall.y * scale,
                    wall.width * scale,
                    wall.height * scale
                );
            }
        });

        // 渲染基地
        if (this.base && this.base.active) {
            this.minimapCtx.fillStyle = '#e74c3c';
            this.minimapCtx.fillRect(
                this.base.x * scale,
                this.base.y * scale,
                this.base.width * scale,
                this.base.height * scale
            );
        }

        // 渲染玩家
        if (this.player && this.player.active) {
            this.minimapCtx.fillStyle = '#4ecdc4';
            this.minimapCtx.fillRect(
                this.player.x * scale,
                this.player.y * scale,
                this.player.width * scale,
                this.player.height * scale
            );
        }

        // 渲染敌人
        this.minimapCtx.fillStyle = '#ff6b35';
        this.enemies.forEach(enemy => {
            if (enemy.active) {
                this.minimapCtx.fillRect(
                    enemy.x * scale,
                    enemy.y * scale,
                    enemy.width * scale,
                    enemy.height * scale
                );
            }
        });

        // 边框
        this.minimapCtx.strokeStyle = '#4ecdc4';
        this.minimapCtx.lineWidth = 2;
        this.minimapCtx.strokeRect(0, 0, 150, 150);
    }

    // 更新UI
    updateUI() {
        // 更新分数
        const scoreElement = document.getElementById('gameScore');
        if (scoreElement) {
            scoreElement.textContent = this.score.toLocaleString();
        }

        // 更新关卡
        const levelElement = document.getElementById('currentLevel');
        if (levelElement) {
            levelElement.textContent = this.currentLevel;
        }

        // 更新生命
        const livesElement = document.getElementById('playerLives');
        if (livesElement) {
            livesElement.textContent = this.lives;
        }

        // 更新敌人数量
        const enemyCountElement = document.getElementById('enemyCount');
        if (enemyCountElement) {
            const remainingEnemies = this.enemySpawnQueue.length + this.enemies.filter(e => e.active).length;
            enemyCountElement.textContent = remainingEnemies;
        }

        // 更新基地血量
        const baseHealthBar = document.getElementById('baseHealthBar');
        if (baseHealthBar && this.base) {
            const healthPercent = (this.base.health / this.base.maxHealth) * 100;
            baseHealthBar.style.width = `${healthPercent}%`;
        }

        // 更新统计信息
        this.updateStatistics();

        // 更新道具显示
        this.updatePowerupDisplay();

        // 更新游戏时间
        this.updateGameTime();
    }

    // 更新统计信息
    updateStatistics() {
        const killCountElement = document.getElementById('killCount');
        if (killCountElement) {
            killCountElement.textContent = this.stats.enemiesKilled;
        }

        const bulletsFiredElement = document.getElementById('bulletsFired');
        if (bulletsFiredElement) {
            bulletsFiredElement.textContent = this.stats.bulletsFired;
        }

        const accuracyElement = document.getElementById('accuracy');
        if (accuracyElement) {
            const accuracy = this.stats.bulletsFired > 0 ?
                Math.round((this.stats.bulletsHit / this.stats.bulletsFired) * 100) : 0;
            accuracyElement.textContent = `${accuracy}%`;
        }
    }

    // 更新道具显示
    updatePowerupDisplay() {
        const powerupContainer = document.getElementById('activePowerups');
        if (!powerupContainer || !window.powerupManager) return;

        const activePowerups = window.powerupManager.getActivePowerups();

        if (activePowerups.length === 0) {
            powerupContainer.innerHTML = '<div class="no-powerups">暂无道具</div>';
            return;
        }

        powerupContainer.innerHTML = activePowerups.map(powerup => `
            <div class="active-powerup">
                <div class="powerup-info">
                    <span class="powerup-icon">${powerup.icon}</span>
                    <span class="powerup-name">${powerup.name}</span>
                </div>
                <div class="powerup-timer">${Math.ceil(powerup.remainingTime / 1000)}s</div>
            </div>
        `).join('');
    }

    // 更新游戏时间
    updateGameTime() {
        const gameTimeElement = document.getElementById('gameTime');
        if (gameTimeElement) {
            const totalSeconds = Math.floor(this.gameTime / 1000);
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            gameTimeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // 键盘事件处理
    handleKeyDown(event) {
        this.keys[event.code] = true;

        // 恢复音频上下文
        if (window.audioManager) {
            window.audioManager.resumeAudioContext();
        }
    }

    handleKeyUp(event) {
        this.keys[event.code] = false;
    }

    // 获取游戏状态
    getGameState() {
        return {
            state: this.gameState,
            level: this.currentLevel,
            score: this.score,
            lives: this.lives,
            gameTime: this.gameTime,
            stats: { ...this.stats },
            difficulty: this.difficulty
        };
    }

    // 设置难度
    setDifficulty(difficulty) {
        if (this.difficultyMultipliers[difficulty]) {
            this.difficulty = difficulty;
            if (window.gameStorage) {
                window.gameStorage.set('difficulty', difficulty);
            }
        }
    }

    // 获取可用皮肤
    getAvailableSkins() {
        const allSkins = [
            { id: 'classic', name: '经典', icon: '🚗', unlocked: true },
            { id: 'military', name: '军用', icon: '🎖️', unlocked: false, requirement: '通过5关' },
            { id: 'futuristic', name: '未来', icon: '🚀', unlocked: false, requirement: '击毁50个敌人' },
            { id: 'stealth', name: '隐形', icon: '👻', unlocked: false, requirement: '命中率80%' },
            { id: 'golden', name: '黄金', icon: '👑', unlocked: false, requirement: '通过10关' }
        ];

        if (window.gameStorage) {
            const unlockedSkins = window.gameStorage.getUnlockedSkins();
            allSkins.forEach(skin => {
                skin.unlocked = unlockedSkins.includes(skin.id);
            });
        }

        return allSkins;
    }

    // 设置玩家皮肤
    setPlayerSkin(skinId) {
        if (window.gameStorage) {
            window.gameStorage.set('playerSkin', skinId);

            // 如果游戏正在进行，更新玩家皮肤
            if (this.player) {
                this.player.skin = skinId;
                this.player.colors = this.player.getSkinColors();
            }
        }
    }

    // 销毁游戏引擎
    destroy() {
        this.stop();

        // 移除事件监听器
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);

        // 停止音频
        if (window.audioManager) {
            window.audioManager.stopBackgroundMusic();
        }

        // 清理效果
        if (window.effectsManager) {
            window.effectsManager.clear();
        }

        console.log('Game engine destroyed');
    }
}

// 创建全局游戏引擎实例
window.gameEngine = new GameEngine();