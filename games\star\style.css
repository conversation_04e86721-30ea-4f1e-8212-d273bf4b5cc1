body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    color: #fff;
    overflow: hidden;
    position: relative;
}

h1 {
    color: #ffd700;
    text-shadow: 2px 2px 8px rgba(255, 215, 0, 0.6);
    font-size: 2.5em;
    margin: 10px 0;
    text-align: center;
}

.container {
    position: relative;
    width: 640px;
    height: 480px;
    border: 3px solid #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    background-color: #000;
    border-radius: 15px;
    overflow: hidden;
}

canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: scaleX(-1);
    border-radius: 12px;
}

#loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5em;
    color: white;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(15, 52, 96, 0.95));
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    z-index: 10;
    text-align: center;
}

/* 游戏UI样式 */
.game-ui {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 5;
    display: none;
}

.score-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
}

.score-item {
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.9), rgba(15, 52, 96, 0.9));
    border: 2px solid #ffd700;
    border-radius: 12px;
    padding: 10px 20px;
    text-align: center;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
    min-width: 120px;
}

.score-item .label {
    display: block;
    font-size: 0.9em;
    color: #ccc;
    margin-bottom: 5px;
}

.score-value {
    font-size: 2em;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

.stars-count {
    font-size: 2em;
    font-weight: bold;
    color: #ff6b6b;
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.8);
}

.combo-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #4ecdc4;
    text-shadow: 0 0 10px rgba(78, 205, 196, 0.8);
}

.caught-count {
    font-size: 2em;
    font-weight: bold;
    color: #ffeb3b;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.8);
}

/* 开始界面样式 */
.start-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(15, 52, 96, 0.95));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
}

.start-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.9), rgba(15, 52, 96, 0.9));
    border: 3px solid #ffd700;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    max-width: 500px;
}

.start-content h2 {
    color: #ffd700;
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
}

.start-content p {
    font-size: 1.1em;
    margin: 10px 0;
    color: #e0e0e0;
}

.start-button {
    background: linear-gradient(135deg, #ffd700, #ff9500);
    border: none;
    border-radius: 50px;
    padding: 15px 40px;
    font-size: 1.5em;
    font-weight: bold;
    color: #1a1a1a;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.start-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

/* 暂停界面样式 */
.pause-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 15;
}

.pause-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(15, 52, 96, 0.95));
    border: 3px solid #ffd700;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.pause-content h2 {
    color: #ffd700;
    margin-bottom: 30px;
}

.resume-button, .restart-button {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 1.2em;
    color: white;
    cursor: pointer;
    margin: 10px;
    transition: all 0.3s ease;
}

.restart-button {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.resume-button:hover, .restart-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 暂停按钮样式 */
.pause-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 215, 0, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5em;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
}

.pause-btn:hover {
    background: #ffd700;
    transform: scale(1.1);
}

/* 粒子效果容器 */
.particle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    pointer-events: none;
    animation: particle-float 3s ease-out forwards;
}

@keyframes particle-float {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(0);
    }
}

/* 星星消失效果 */
.star-explosion {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0) 70%);
    pointer-events: none;
    animation: star-explosion 0.6s ease-out forwards;
}

@keyframes star-explosion {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: scale(2) rotate(360deg);
        opacity: 0;
    }
}

/* 分数飞入效果 */
.score-popup {
    position: absolute;
    font-size: 1.5em;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
    pointer-events: none;
    z-index: 12;
    animation: score-popup 1.5s ease-out forwards;
}

@keyframes score-popup {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-80px) scale(1.5);
        opacity: 0;
    }
} 