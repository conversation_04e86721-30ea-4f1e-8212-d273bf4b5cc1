* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%);
    background-attachment: fixed;
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.game-title {
    font-size: 2.5em;
    margin: 0 0 20px 0;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd23f, #4ecdc4);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 开始界面 */
.start-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.start-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(15, 32, 39, 0.95), rgba(44, 83, 100, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
}

.start-content h2 {
    color: #4ecdc4;
    font-size: 2.2em;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
}

.game-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 30px 0;
}

.feature {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 1.1em;
    transition: all 0.3s ease;
}

.feature:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.difficulty-selection {
    margin: 30px 0;
}

.difficulty-selection h3 {
    color: #ffd23f;
    margin-bottom: 15px;
}

.difficulty-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.difficulty-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1em;
}

.difficulty-btn:hover, .difficulty-btn.active {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-color: #ff6b35;
    transform: translateY(-2px);
}

.start-button, .skin-button {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.3em;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 15px rgba(78, 205, 196, 0.4);
    margin: 10px;
}

.skin-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 15px rgba(102, 126, 234, 0.4);
}

.start-button:hover, .skin-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 20px rgba(78, 205, 196, 0.6);
}

/* 皮肤选择界面 */
.skin-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.skin-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(15, 32, 39, 0.95), rgba(44, 83, 100, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    max-width: 700px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.skin-content h2 {
    color: #667eea;
    margin-bottom: 30px;
    font-size: 2em;
}

.skin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.skin-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.skin-option:hover, .skin-option.selected {
    background: rgba(102, 126, 234, 0.3);
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(102, 126, 234, 0.4);
}

.skin-preview {
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
}

.skin-name {
    font-size: 0.9em;
    color: #e0e0e0;
}

.skin-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.confirm-skin-btn, .cancel-skin-btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1em;
    transition: all 0.3s ease;
}

.confirm-skin-btn {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.cancel-skin-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.confirm-skin-btn:hover, .cancel-skin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* 暂停界面 */
.pause-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.pause-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(15, 32, 39, 0.95), rgba(44, 83, 100, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
}

.pause-content h2 {
    color: #ffd23f;
    margin-bottom: 30px;
    font-size: 2em;
}

.pause-stats {
    margin: 30px 0;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 1.1em;
}

.pause-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.resume-button, .restart-button, .menu-button {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.restart-button {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.menu-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.resume-button:hover, .restart-button:hover, .menu-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* 游戏结束界面 */
.game-over-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.game-over-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.95), rgba(238, 90, 36, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    max-width: 500px;
    width: 90%;
}

.game-over-content h2 {
    color: #ffffff;
    margin-bottom: 30px;
    font-size: 2.2em;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.final-stats {
    margin: 30px 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    font-size: 1.2em;
}

.score-value, .level-value, .kills-value, .high-score-value {
    font-weight: bold;
    color: #ffd23f;
}

.achievements {
    margin: 30px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

.achievements h3 {
    color: #ffd23f;
    margin-bottom: 15px;
}

.play-again-button, .back-menu-button {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2em;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px;
}

.back-menu-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.play-again-button:hover, .back-menu-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(78, 205, 196, 0.6);
}

/* 关卡完成界面 */
.level-complete-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.level-complete-content {
    text-align: center;
    background: linear-gradient(135deg, rgba(78, 205, 196, 0.95), rgba(68, 160, 141, 0.95));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    max-width: 500px;
    width: 90%;
}

.level-complete-content h2 {
    color: #ffffff;
    margin-bottom: 30px;
    font-size: 2.2em;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.level-stats {
    margin: 30px 0;
}

.bonus-value, .total-value {
    font-weight: bold;
    color: #ffd23f;
}

.next-level-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2em;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.next-level-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(102, 126, 234, 0.6);
}

/* 主游戏界面 */
.game-area {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    max-width: 1400px;
    position: relative;
    z-index: 1;
}

.info-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 200px;
}

.left-panel, .right-panel {
    min-width: 220px;
}

.info-section, .stats-section {
    margin-bottom: 25px;
}

.info-section h3, .stats-section h3 {
    color: #4ecdc4;
    margin: 0 0 15px 0;
    font-size: 1.1em;
    text-align: center;
}

.status-grid {
    display: grid;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(78, 205, 196, 0.2);
}

.status-label {
    color: #b8b8b8;
    font-size: 0.9em;
}

.status-value {
    color: #ffd23f;
    font-weight: bold;
}

.powerup-display {
    min-height: 80px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-powerups {
    color: #888;
    text-align: center;
    font-style: italic;
}

.active-powerup {
    background: rgba(255, 211, 61, 0.2);
    border: 1px solid #ffd23f;
    border-radius: 6px;
    padding: 8px;
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.powerup-name {
    color: #ffd23f;
    font-weight: bold;
}

.powerup-timer {
    color: #ff6b35;
    font-size: 0.8em;
}

.achievement-display {
    max-height: 150px;
    overflow-y: auto;
}

.achievement-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 8px;
    margin: 5px 0;
    border-left: 3px solid #4ecdc4;
}

.achievement-name {
    color: #4ecdc4;
    font-size: 0.9em;
    font-weight: bold;
}

.achievement-progress {
    color: #b8b8b8;
    font-size: 0.8em;
}

/* 主游戏区域 */
.main-game {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 832px;
    padding: 0 20px;
}

.score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score-label {
    color: #b8b8b8;
    font-size: 0.9em;
}

.score-value {
    color: #ffd23f;
    font-size: 1.8em;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 211, 61, 0.5);
}

.base-health {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.base-label {
    color: #b8b8b8;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.health-bar {
    width: 150px;
    height: 12px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
    width: 100%;
    transition: width 0.3s ease;
    border-radius: 6px;
}

#gameCanvas {
    background: #2c3e50;
    border: 3px solid #4ecdc4;
    border-radius: 10px;
    box-shadow: 0 0 30px rgba(78, 205, 196, 0.3);
}

.game-controls {
    display: flex;
    gap: 15px;
}

.control-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(102, 126, 234, 0.4);
}

/* 统计面板 */
.stat-item {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    color: #b8b8b8;
    font-size: 0.9em;
}

.stat-value {
    color: #ffd23f;
    font-weight: bold;
    font-size: 1.1em;
}

/* 操作说明面板 */
.controls-section {
    margin-top: 20px;
}

.controls-section h3 {
    color: #4ecdc4;
    margin-bottom: 15px;
    font-size: 1.1em;
    text-align: center;
}

.control-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-list .control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(78, 205, 196, 0.2);
    transition: all 0.3s ease;
}

.control-list .control-item:hover {
    background: rgba(78, 205, 196, 0.1);
    border-color: rgba(78, 205, 196, 0.4);
}

.control-list .key {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.8em;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-list .action {
    color: #e0e0e0;
    font-size: 0.85em;
    flex: 1;
    text-align: right;
}

/* 小地图 */
.minimap-section {
    margin-top: 20px;
}

.minimap-section h3 {
    color: #4ecdc4;
    margin-bottom: 15px;
    font-size: 1.1em;
    text-align: center;
}

#minimapCanvas {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(78, 205, 196, 0.5);
    border-radius: 8px;
    display: block;
    margin: 0 auto;
}

/* 粒子效果 */
.particle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 500;
}

.particle {
    position: absolute;
    pointer-events: none;
    border-radius: 50%;
}

.explosion-particle {
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, #ff6b35, #f7931e);
    animation: explosion-particle 1s ease-out forwards;
}

.spark-particle {
    width: 3px;
    height: 3px;
    background: #ffd23f;
    animation: spark-particle 0.8s ease-out forwards;
}

.smoke-particle {
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, rgba(100, 100, 100, 0.8), transparent);
    animation: smoke-particle 2s ease-out forwards;
}

@keyframes explosion-particle {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(0.3) translateY(-50px);
    }
}

@keyframes spark-particle {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0);
    }
}

@keyframes smoke-particle {
    0% {
        opacity: 0.8;
        transform: scale(0.5) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(2) translateY(-80px);
    }
}

/* 屏幕震动效果 */
.screen-shake {
    animation: screen-shake 0.3s ease-in-out;
}

@keyframes screen-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 击中效果 */
.hit-effect {
    position: absolute;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.8), transparent);
    border-radius: 50%;
    pointer-events: none;
    animation: hit-flash 0.3s ease-out forwards;
}

@keyframes hit-flash {
    0% {
        opacity: 1;
        transform: scale(0.5);
    }
    100% {
        opacity: 0;
        transform: scale(2);
    }
}

/* 道具闪烁效果 */
.powerup-glow {
    animation: powerup-glow 1.5s ease-in-out infinite;
}

@keyframes powerup-glow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 211, 61, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 211, 61, 0.8);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .game-area {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .info-panel {
        width: 100%;
        max-width: 832px;
        display: flex;
        justify-content: space-around;
    }

    .info-section, .stats-section {
        flex: 1;
        margin: 0 10px;
    }
}

@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }

    .game-title {
        font-size: 2em;
    }

    .start-content, .pause-content, .game-over-content {
        padding: 30px 20px;
    }

    .game-features {
        grid-template-columns: 1fr;
    }

    .difficulty-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .difficulty-btn {
        width: 100%;
    }

    #gameCanvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }

    .game-header {
        flex-direction: column;
        gap: 15px;
    }

    .info-panel {
        flex-direction: column;
    }

    .skin-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .start-button, .skin-button {
        padding: 12px 30px;
        font-size: 1.1em;
    }

    .control-btn {
        padding: 8px 15px;
        font-size: 0.8em;
    }

    .skin-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pause-actions {
        gap: 10px;
    }

    .resume-button, .restart-button, .menu-button {
        padding: 10px 25px;
        font-size: 1em;
        min-width: 120px;
    }
}
