<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天上掉星星</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>徒手摘星辰</h1>
    
    <!-- 游戏UI界面 -->
    <div id="gameUI" class="game-ui">
        <div class="score-container">
            <div class="score-item">
                <span class="label">分数</span>
                <span id="score" class="score-value">0</span>
            </div>
            <div class="score-item">
                <span class="label">手上星星</span>
                <span id="starsInHand" class="stars-count">0</span>
            </div>
            <div class="score-item">
                <span class="label">正在抓取</span>
                <span id="caughtStars" class="caught-count">0</span>
            </div>
            <div class="score-item">
                <span class="label">连击</span>
                <span id="combo" class="combo-value">x1</span>
            </div>
        </div>
    </div>

    <!-- 开始游戏界面 -->
    <div id="startScreen" class="start-screen">
        <div class="start-content">
            <h2>🌟 徒手摘星辰 🌟</h2>
            <p>用手接住掉落的星星来获得分数！</p>
            <p>🌟 握拳可同时抓住多个星星</p>
            <p>⭐ 手上星星越多，分数越高</p>
            <p>💫 星星会在一段时间后消失</p>
            <p>✨ 保持连击获得更高分数</p>
            <button id="startBtn" class="start-button">开始游戏</button>
        </div>
    </div>

    <!-- 游戏暂停界面 -->
    <div id="pauseScreen" class="pause-screen" style="display: none;">
        <div class="pause-content">
            <h2>游戏暂停</h2>
            <button id="resumeBtn" class="resume-button">继续游戏</button>
            <button id="restartBtn" class="restart-button">重新开始</button>
        </div>
    </div>

    <div class="container">
        <video id="video" autoplay playsinline style="display:none;"></video>
        <canvas id="canvas"></canvas>
        
        <!-- 暂停按钮 -->
        <button id="pauseBtn" class="pause-btn" style="display: none;">⏸️</button>
    </div>
    
    <div id="loading">正在加载模型和摄像头...</div>

    <!-- 粒子效果容器 -->
    <div id="particleContainer" class="particle-container"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.19.0/matter.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/hands/hands.js" crossorigin="anonymous"></script>
    <script src="script.js"></script>
</body>
</html> 