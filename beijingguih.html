<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京故宫周末2天旅游攻略</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px 20px;
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        header h1 {
            font-size: 2.8rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        .weather-section, .tips-section, .map-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .weather-title, .tips-title, .map-title {
            font-size: 1.6rem;
            margin-bottom: 20px;
            color: #ff6b6b;
            border-bottom: 3px solid #f0f0f0;
            padding-bottom: 12px;
        }

        .weather-cards {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
        }

        .weather-card {
            flex: 1;
            min-width: 280px;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .weather-card h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .weather-icon {
            font-size: 2.5rem;
            margin: 15px 0;
        }

        .temp {
            font-size: 1.4rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .day-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            margin-bottom: 35px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .day-header {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px 25px;
            font-size: 1.4rem;
        }

        .day-content {
            padding: 25px;
        }

        .spot-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
            margin-top: 20px;
        }

        .spot-card {
            flex: 1;
            min-width: 300px;
            max-width: 380px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .spot-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .spot-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .spot-info {
            padding: 20px;
        }

        .spot-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #6c5ce7;
        }

        .spot-desc {
            font-size: 1rem;
            color: #666;
            margin-bottom: 12px;
            line-height: 1.6;
        }

        .transportation {
            display: flex;
            align-items: center;
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            border-radius: 12px;
        }

        .transportation-icon {
            font-size: 2rem;
            margin-right: 20px;
        }

        .map-container {
            height: 500px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .taxi-links {
            margin-top: 20px;
        }

        .taxi-btn {
            display: inline-block;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            margin-right: 15px;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .taxi-btn:hover {
            background: linear-gradient(135deg, #00a085, #00b894);
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
        }

        .tips-content ul {
            padding-left: 25px;
        }

        .tips-content li {
            margin-bottom: 12px;
            line-height: 1.7;
        }

        footer {
            text-align: center;
            padding: 25px;
            color: white;
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .weather-cards, .spot-cards {
                flex-direction: column;
            }
            
            .spot-card {
                max-width: 100%;
            }
            
            header h1 {
                font-size: 2.2rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>🏛️ 北京故宫周末2天旅游攻略</h1>
            <p>从亦庄出发，探寻皇城古韵，品味京城文化</p>
        </header>

        <section class="weather-section">
            <h2 class="weather-title">周末天气预报</h2>
            <div class="weather-cards">
                <div class="weather-card">
                    <h3>6月14日（周六）</h3>
                    <div class="weather-icon">⛈️</div>
                    <p>白天: 雷阵雨</p>
                    <p>夜间: 雷阵雨</p>
                    <p class="temp">温度: 30°C/21°C</p>
                    <p>风力: 西北风 1-3级</p>
                </div>
                <div class="weather-card">
                    <h3>6月15日（周日）</h3>
                    <div class="weather-icon">☁️</div>
                    <p>白天: 阴</p>
                    <p>夜间: 阴</p>
                    <p class="temp">温度: 30°C/20°C</p>
                    <p>风力: 西北风 1-3级</p>
                </div>
            </div>
        </section>

        <section class="tips-section">
            <h2 class="tips-title">🎯 出行小贴士</h2>
            <div class="tips-content">
                <ul>
                    <li><strong>出发时间：</strong>建议早上8:00从亦庄出发，避开高峰期，预计1小时到达故宫。</li>
                    <li><strong>雨天准备：</strong>周六有雷阵雨，请携带雨具，建议穿防滑鞋。</li>
                    <li><strong>停车建议：</strong>故宫周边停车位紧张，建议选择王府世纪地下停车场或普度寺遗址停车场。</li>
                    <li><strong>门票预约：</strong>故宫门票需要提前网上预约，建议关注故宫博物院官方微信。</li>
                    <li><strong>美食推荐：</strong>四季民福烤鸭、全聚德、老北京炸酱面等京味美食不容错过。</li>
                    <li><strong>游览建议：</strong>故宫很大，建议重点游览太和殿、中和殿、保和殿三大殿。</li>
                </ul>
            </div>
        </section>

        <section class="map-section">
            <h2 class="map-title">🗺️ 旅游路线地图</h2>
            <div class="map-container" id="container"></div>
            <div style="margin-top: 25px; text-align: center;">
                <a href="https://surl.amap.com/ECgqe25RaRc" class="taxi-btn"
                    style="font-size: 1.3rem; padding: 18px 30px;">
                    📍 查看完整旅游地图
                </a>
            </div>
        </section>

        <section class="itinerary-section">
            <div class="day-container">
                <div class="day-header">
                    <h2>Day 1: 故宫深度游 (6月14日)</h2>
                </div>
                <div class="day-content">
                    <p>第一天专注故宫博物院的深度游览，感受明清皇家宫殿的恢弘气势。</p>

                    <div class="spot-cards">
                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/2f968490d105bb2741e17f90b85c6b79"
                                alt="故宫博物院" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">故宫博物院</h3>
                                <p class="spot-desc">明清两朝的皇家宫殿，现为世界文化遗产，收藏着丰富的古代文物和艺术品。</p>
                                <p><strong>游览时间：</strong>4-5小时</p>
                                <p><strong>门票：</strong>60元（网上预约）</p>
                            </div>
                        </div>

                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/f6f696fc0c5b06ba3d0a639f17a03d6a"
                                alt="太和殿" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">太和殿</h3>
                                <p class="spot-desc">故宫中最重要的建筑，皇帝举行大典的地方，也是中国古代建筑的杰出代表。</p>
                                <p><strong>特色：</strong>金銮宝座、九龙壁</p>
                                <p><strong>最佳拍照点：</strong>太和门广场</p>
                            </div>
                        </div>

                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/9446d40cf3b66fd2e17a90b6bfd1e83b"
                                alt="保和殿" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">保和殿</h3>
                                <p class="spot-desc">明清两朝举行殿试的地方，殿后有著名的云龙石雕，是故宫中最大的石雕。</p>
                                <p><strong>亮点：</strong>云龙石雕、科举文化</p>
                                <p><strong>游览建议：</strong>注意观赏殿内陈设</p>
                            </div>
                        </div>
                    </div>

                    <div class="transportation">
                        <div class="transportation-icon">🚗</div>
                        <div>
                            <h3>停车场推荐</h3>
                            <p><strong>王府世纪地下停车场：</strong>距离故宫500米，15元/小时</p>
                            <p><strong>普度寺遗址地下停车场：</strong>距离故宫800米，10元/小时</p>
                            <div class="taxi-links">
                                <a href="amapuri://navi?sourceApplication=amap_mcp&lon=116.397029&lat=39.917839&dev=1&style=2"
                                    class="taxi-btn">导航去故宫</a>
                            </div>
                        </div>
                    </div>

                    <h3 style="color: #e84393; margin: 25px 0 15px 0;">🍽️ 推荐美食</h3>
                    <div class="spot-cards">
                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/4b7d9bedcbdb8eff378f08fef54cfed2"
                                alt="四季民福烤鸭店" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">四季民福烤鸭店(故宫店)</h3>
                                <p class="spot-desc">正宗北京烤鸭，皮脆肉嫩，配以甜面酱、黄瓜丝，是来北京必尝的美食。</p>
                                <p><strong>人均消费：</strong>150-200元</p>
                                <p><strong>地址：</strong>南池子大街11号</p>
                            </div>
                        </div>

                        <div class="spot-card">
                            <img src="https://aos-comment.amap.com/B0FFFTCGNX/comment/ad2a0c732a314db555aa0538af92d914_2048_2048_80.jpg"
                                alt="故宫餐厅" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">故宫餐厅</h3>
                                <p class="spot-desc">位于故宫内部的餐厅，提供宫廷风味菜肴，环境优雅，体验独特。</p>
                                <p><strong>特色：</strong>宫廷御膳、文创套餐</p>
                                <p><strong>位置：</strong>保和殿东侧景运门外</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="day-container">
                <div class="day-header">
                    <h2>Day 2: 周边文化游 (6月15日)</h2>
                </div>
                <div class="day-content">
                    <p>第二天游览故宫周边的文化景点，深入体验北京的历史文化。</p>

                    <div class="spot-cards">
                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/fa8e8a4da310b3053ec6fc72"
                                alt="景山公园" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">景山公园</h3>
                                <p class="spot-desc">位于故宫北面，是观赏故宫全景的最佳地点，山顶的万春亭可俯瞰紫禁城。</p>
                                <p><strong>门票：</strong>2元</p>
                                <p><strong>游览时间：</strong>1-2小时</p>
                            </div>
                        </div>

                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/c2b46e1ea1da0c0c0c50c2bcb0c3e5e5"
                                alt="北海公园" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">北海公园</h3>
                                <p class="spot-desc">中国现存最古老、最完整的皇家园林，以白塔为中心，湖光山色美不胜收。</p>
                                <p><strong>门票：</strong>10元</p>
                                <p><strong>特色：</strong>白塔、九龙壁</p>
                            </div>
                        </div>

                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/d4b1b7e92d59f8bd556b7ad8456f9740"
                                alt="天安门广场" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">天安门广场</h3>
                                <p class="spot-desc">世界上最大的城市广场，见证了新中国的诞生，是每个中国人心中的圣地。</p>
                                <p><strong>免费开放</strong></p>
                                <p><strong>注意：</strong>需安检，禁带违禁物品</p>
                            </div>
                        </div>
                    </div>

                    <div class="transportation">
                        <div class="transportation-icon">🚶‍♂️</div>
                        <div>
                            <h3>景点间交通</h3>
                            <p>这些景点都在故宫周边，步行即可到达，景山公园就在故宫正北方，北海公园在西北方向。</p>
                            <div class="taxi-links">
                                <a href="amapuri://navi?sourceApplication=amap_mcp&lon=116.393831&lat=39.928379&dev=1&style=2"
                                    class="taxi-btn">导航去景山公园</a>
                                <a href="amapuri://navi?sourceApplication=amap_mcp&lon=116.389052&lat=39.926214&dev=1&style=2"
                                    class="taxi-btn">导航去北海公园</a>
                            </div>
                        </div>
                    </div>

                    <h3 style="color: #e84393; margin: 25px 0 15px 0;">🍜 更多美食推荐</h3>
                    <div class="spot-cards">
                        <div class="spot-card">
                            <img src="http://store.is.autonavi.com/showpic/980da79fb3d5a56df1e7f3ea3f67b3df"
                                alt="老北京炸酱面" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">老北京炸酱面(南池子大街店)</h3>
                                <p class="spot-desc">正宗的老北京炸酱面，面条劲道，炸酱香浓，配以黄瓜丝、豆芽菜等配菜。</p>
                                <p><strong>人均消费：</strong>30-50元</p>
                                <p><strong>推荐：</strong>炸酱面、打卤面</p>
                            </div>
                        </div>

                        <div class="spot-card">
                            <img src="https://aos-comment.amap.com/B0KRVKE6OE/comment/content_media_external_images_media_100009716_1738853539615_49116772.jpg"
                                alt="全聚德" class="spot-img">
                            <div class="spot-info">
                                <h3 class="spot-name">全聚德(南池子大街店)</h3>
                                <p class="spot-desc">百年老字号烤鸭店，传统挂炉烤鸭工艺，鸭皮酥脆，鸭肉鲜美。</p>
                                <p><strong>人均消费：</strong>200-300元</p>
                                <p><strong>特色：</strong>挂炉烤鸭、鸭汤</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <footer>
            <p>© 2025 北京故宫周末旅游攻略 | 制作时间: 2025年6月</p>
            <p>🚗 从北京亦庄大族广场出发，驾车约1小时到达故宫</p>
        </footer>
    </div>

    <script>
        window.onload = function () {
            var url = 'https://webapi.amap.com/maps?v=2.0&key=e8f742bdb09f99c8c6b035b7f1f04e66';
            var jsapi = document.createElement('script');
            jsapi.charset = 'utf-8';
            jsapi.src = url;
            document.head.appendChild(jsapi);

            jsapi.onload = function () {
                initMap();
            };
        }

        function initMap() {
            var map = new AMap.Map('container', {
                zoom: 11,
                center: [116.454, 39.854],
                viewMode: '3D',
                pitch: 20
            });

            var dayColors = ['#ff6b6b', '#a29bfe', '#74b9ff', '#00b894'];

            // 起点：亦庄大族广场
            var startMarker = new AMap.Marker({
                position: [116.512841, 39.791810],
                title: '出发点：亦庄大族广场',
                map: map,
                icon: new AMap.Icon({
                    size: new AMap.Size(32, 32),
                    image: 'https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png',
                    imageSize: new AMap.Size(32, 32)
                })
            });

            // 故宫及周边景点
            var attractions = [
                { position: [116.397029, 39.917839], title: '故宫博物院', day: '第一天主要景点' },
                { position: [116.393831, 39.928379], title: '景山公园', day: '第二天：俯瞰故宫全景' },
                { position: [116.389052, 39.926214], title: '北海公园', day: '第二天：皇家园林' },
                { position: [116.391837, 39.905968], title: '天安门广场', day: '第二天：历史地标' }
            ];

            // 停车场
            var parkings = [
                { position: [116.405472, 39.912506], title: '王府世纪地下停车场', price: '15元/小时' },
                { position: [116.394587, 39.914032], title: '普度寺遗址地下停车场', price: '10元/小时' }
            ];

            // 美食
            var restaurants = [
                { position: [116.394845, 39.915623], title: '四季民福烤鸭店(故宫店)', type: '北京烤鸭' },
                { position: [116.397029, 39.917839], title: '故宫餐厅', type: '宫廷菜' },
                { position: [116.394923, 39.916845], title: '老北京炸酱面', type: '传统小吃' }
            ];

            // 添加景点标记
            attractions.forEach(function (item, index) {
                var marker = new AMap.Marker({
                    position: item.position,
                    title: item.title,
                    map: map,
                    icon: new AMap.Icon({
                        size: new AMap.Size(28, 28),
                        image: 'https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-' + (index % 4 + 1) + '.png',
                        imageSize: new AMap.Size(28, 28)
                    })
                });

                var infoWindow = new AMap.InfoWindow({
                    content: '<div style="padding:12px;font-size:14px;">' +
                        '<b style="color:' + dayColors[index % 4] + '">' + item.title + '</b><br/>' +
                        item.day + '</div>',
                    offset: new AMap.Pixel(0, -30)
                });

                marker.on('click', function () {
                    infoWindow.open(map, marker.getPosition());
                });
            });

            // 添加停车场标记
            parkings.forEach(function (item) {
                var marker = new AMap.Marker({
                    position: item.position,
                    title: item.title,
                    map: map,
                    icon: new AMap.Icon({
                        size: new AMap.Size(24, 24),
                        image: 'https://a.amap.com/jsapi_demos/static/demo-center/icons/parking.png',
                        imageSize: new AMap.Size(24, 24)
                    })
                });

                var infoWindow = new AMap.InfoWindow({
                    content: '<div style="padding:10px;font-size:13px;">' +
                        '<b>🅿️ ' + item.title + '</b><br/>' +
                        '收费标准：' + item.price + '</div>',
                    offset: new AMap.Pixel(0, -30)
                });

                marker.on('click', function () {
                    infoWindow.open(map, marker.getPosition());
                });
            });

            // 添加美食标记
            restaurants.forEach(function (item) {
                var marker = new AMap.Marker({
                    position: item.position,
                    title: item.title,
                    map: map,
                    icon: new AMap.Icon({
                        size: new AMap.Size(24, 24),
                        image: 'https://a.amap.com/jsapi_demos/static/demo-center/icons/restaurant.png',
                        imageSize: new AMap.Size(24, 24)
                    })
                });

                var infoWindow = new AMap.InfoWindow({
                    content: '<div style="padding:10px;font-size:13px;">' +
                        '<b>🍽️ ' + item.title + '</b><br/>' +
                        '类型：' + item.type + '</div>',
                    offset: new AMap.Pixel(0, -30)
                });

                marker.on('click', function () {
                    infoWindow.open(map, marker.getPosition());
                });
            });

            // 绘制从亦庄到故宫的路线
            var drivingPath = new AMap.Polyline({
                path: [
                    [116.512841, 39.791810], // 亦庄大族广场
                    [116.397029, 39.917839]  // 故宫
                ],
                strokeColor: '#ff6b6b',
                strokeWeight: 4,
                strokeStyle: 'solid',
                strokeOpacity: 0.8,
                map: map
            });

            // 添加图例
            addLegend(map);
        }

        function addLegend(map) {
            var legend = document.createElement('div');
            legend.style.cssText = 'position:absolute;bottom:20px;left:20px;padding:15px;background:rgba(255,255,255,0.95);border-radius:8px;box-shadow:0 4px 12px rgba(0,0,0,0.15);font-size:12px;max-width:200px;z-index:100;';

            var html = '<div style="font-weight:bold;margin-bottom:8px;color:#333;">图例说明</div>';
            html += '<div style="margin:4px 0;"><span style="display:inline-block;width:16px;height:16px;background:#ff6b6b;border-radius:50%;margin-right:8px;vertical-align:middle;"></span>出发点</div>';
            html += '<div style="margin:4px 0;"><span style="display:inline-block;width:16px;height:16px;background:#74b9ff;border-radius:50%;margin-right:8px;vertical-align:middle;"></span>旅游景点</div>';
            html += '<div style="margin:4px 0;"><span style="display:inline-block;width:16px;height:16px;background:#00b894;border-radius:50%;margin-right:8px;vertical-align:middle;"></span>停车场</div>';
            html += '<div style="margin:4px 0;"><span style="display:inline-block;width:16px;height:16px;background:#e84393;border-radius:50%;margin-right:8px;vertical-align:middle;"></span>美食推荐</div>';

            legend.innerHTML = html;
            document.getElementById('container').appendChild(legend);
        }
    </script>
</body>

</html> 