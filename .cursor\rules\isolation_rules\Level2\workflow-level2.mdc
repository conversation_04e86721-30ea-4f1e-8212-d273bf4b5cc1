---
description: Basic workflow for Level 2 Simple Enhancement tasks
globs: "**/level2/**", "**/workflow/**"
alwaysApply: false
---
# WORKFLOW FOR LEVEL 2 TASKS

> **TL;DR:** This document outlines a structured yet efficient workflow for Level 2 (Simple Enhancement) tasks, including 6 key phases with milestone checkpoints and quality verification.

## 🔍 LEVEL 2 WORKFLOW OVERVIEW

```mermaid
graph LR
    Init["1. INITIALIZATION"] --> Doc["2. DOCUMENTATION<br>SETUP"]
    Doc --> Plan["3. TASK<br>PLANNING"]
    Plan --> Impl["4. IMPLEMENTATION"]
    Impl --> Reflect["5. REFLECTION"]
    Reflect --> Archive["6. ARCHIVING"]
    
    %% Document connections for each phase
    Init -.-> InitDocs["INITIALIZATION"]
    Doc -.-> DocDocs["DOCUMENTATION"]
    Plan -.-> PlanDocs["PLANNING"]
    Impl -.-> ImplDocs["IMPLEMENTATION"]
    Reflect -.-> ReflectDocs["REFLECTION"]
    Archive -.-> ArchiveDocs["ARCHIVING"]
```

Level 2 tasks involve simple enhancements that require a structured approach with moderate planning and documentation. This workflow provides the right balance of process and efficiency.

## 📋 WORKFLOW PHASES

### Phase 1: INITIALIZATION

```mermaid
graph TD
    Start["Start Level 2 Task"] --> Platform{"Detect<br>Platform"}
    Platform --> FileCheck["Critical File<br>Verification"]
    FileCheck --> LoadStructure["Load Memory<br>Bank Structure"]
    LoadStructure --> TaskCreation["Create Task<br>in tasks.md"]
    TaskCreation --> SetupComplete["Initialization<br>Complete"]
```

**Steps:**
1. Platform detection
2. Critical file verification
3. Memory Bank structure loading
4. Task creation in tasks.md
5. Initial task scope definition

**Milestone Checkpoint:**
```
✓ INITIALIZATION CHECKPOINT
- Platform detected and configured? [YES/NO]
- Critical files verified? [YES/NO]
- Memory Bank loaded? [YES/NO]
- Task created in tasks.md? [YES/NO]
- Initial scope defined? [YES/NO]

→ If all YES: Proceed to Documentation Setup
→ If any NO: Complete initialization steps
```

### Phase 2: DOCUMENTATION SETUP

```mermaid
graph TD
    Start["Begin Documentation<br>Setup"] --> LoadTemplate["Load Basic<br>Documentation Templates"]
    LoadTemplate --> UpdateProject["Update<br>projectbrief.md"]
    UpdateProject --> UpdateContext["Update<br>activeContext.md"]
    UpdateContext --> SetupComplete["Documentation<br>Setup Complete"]
```

**Steps:**
1. Load basic documentation templates
2. Update projectbrief.md with enhancement details
3. Update activeContext.md with current focus
4. Create minimal documentation structure

**Milestone Checkpoint:**
```
✓ DOCUMENTATION CHECKPOINT
- Documentation templates loaded? [YES/NO]
- projectbrief.md updated? [YES/NO]
- activeContext.md updated? [YES/NO]
- Documentation structure created? [YES/NO]

→ If all YES: Proceed to Task Planning
→ If any NO: Complete documentation setup
```

### Phase 3: TASK PLANNING

```mermaid
graph TD
    Start["Begin Task<br>Planning"] --> Requirements["Define Clear<br>Requirements"]
    Requirements --> SubTasks["Break Down<br>Into Subtasks"]
    SubTasks --> TasksUpdate["Update tasks.md<br>With Subtasks"]
    TasksUpdate --> TimeEstimate["Create Time<br>Estimates"]
    TimeEstimate --> PlanComplete["Planning<br>Complete"]
```

**Steps:**
1. Define clear requirements
2. Break down into subtasks
3. Update tasks.md with subtasks
4. Create time estimates
5. Document dependencies and constraints

**Milestone Checkpoint:**
```
✓ PLANNING CHECKPOINT
- Requirements clearly defined? [YES/NO]
- Task broken down into subtasks? [YES/NO]
- tasks.md updated with subtasks? [YES/NO]
- Time estimates created? [YES/NO]
- Dependencies documented? [YES/NO]

→ If all YES: Proceed to Implementation
→ If any NO: Complete planning steps
```

### Phase 4: IMPLEMENTATION

```mermaid
graph TD
    Start["Begin<br>Implementation"] --> SubTask1["Complete<br>Subtask 1"]
    SubTask1 --> UpdateStatus1["Update Status<br>in tasks.md"]
    UpdateStatus1 --> SubTask2["Complete<br>Subtask 2"]
    SubTask2 --> UpdateStatus2["Update Status<br>in tasks.md"]
    UpdateStatus2 --> FinalSubTask["Complete<br>Final Subtask"]
    FinalSubTask --> Verification["Perform<br>Verification"]
    Verification --> ImplComplete["Implementation<br>Complete"]
```

**Steps:**
1. Implement first subtask
2. Update status in tasks.md
3. Implement remaining subtasks
4. Regular status updates after each subtask
5. Verify complete implementation

**Milestone Checkpoint:**
```
✓ IMPLEMENTATION CHECKPOINT
- All subtasks completed? [YES/NO]
- Status updates maintained? [YES/NO]
- Enhancement fully implemented? [YES/NO]
- Basic verification performed? [YES/NO]
- tasks.md fully updated? [YES/NO]

→ If all YES: Proceed to Reflection
→ If any NO: Complete implementation steps
```

### Phase 5: REFLECTION

```mermaid
graph TD
    Start["Begin<br>Reflection"] --> Template["Load Reflection<br>Template"]
    Template --> Review["Review Completed<br>Enhancement"]
    Review --> Document["Document Successes<br>and Challenges"]
    Document --> Insights["Extract Key<br>Insights"]
    Insights --> Actions["Define Action<br>Items"]
    Actions --> ReflectComplete["Reflection<br>Complete"]
```

**Steps:**
1. Load reflection template
2. Review completed enhancement
3. Document successes and challenges
4. Extract key insights
5. Define action items for future work

**Milestone Checkpoint:**
```
✓ REFLECTION CHECKPOINT
- Reflection template loaded? [YES/NO]
- Enhancement reviewed? [YES/NO]
- Successes and challenges documented? [YES/NO]
- Key insights extracted? [YES/NO]
- Action items defined? [YES/NO]

→ If all YES: Proceed to Archiving
→ If any NO: Complete reflection steps
```

### Phase 6: ARCHIVING

```mermaid
graph TD
    Start["Begin<br>Archiving"] --> Template["Load Archive<br>Template"]
    Template --> Gather["Gather Implementation<br>Details"]
    Gather --> Create["Create Archive<br>Document"]
    Create --> CrossRef["Create Cross-<br>References"]
    CrossRef --> Update["Update Memory<br>Bank Files"]
    Update --> ArchiveComplete["Archiving<br>Complete"]
```

**Steps:**
1. Load archive template
2. Gather implementation details
3. Create archive document
4. Create cross-references
5. Update Memory Bank files

**Milestone Checkpoint:**
```
✓ ARCHIVING CHECKPOINT
- Archive template loaded? [YES/NO]
- Implementation details gathered? [YES/NO]
- Archive document created? [YES/NO]
- Cross-references created? [YES/NO]
- Memory Bank files updated? [YES/NO]

→ If all YES: Task Complete
→ If any NO: Complete archiving steps
```

## 📋 WORKFLOW VERIFICATION CHECKLIST

```
✓ FINAL WORKFLOW VERIFICATION
- All phases completed? [YES/NO]
- All milestone checkpoints passed? [YES/NO]
- tasks.md fully updated? [YES/NO]
- Reflection document created? [YES/NO]
- Archive document created? [YES/NO]
- Memory Bank fully updated? [YES/NO]

→ If all YES: Level 2 Task Successfully Completed
→ If any NO: Address outstanding items
```

## 📋 MINIMAL MODE WORKFLOW

For minimal mode, use this streamlined workflow:

```
1. INIT: Verify environment, create task entry
2. DOCS: Update projectbrief and activeContext
3. PLAN: Define requirements, subtasks, estimates
4. IMPL: Complete subtasks, update status
5. REFLECT: Document key insights and actions
6. ARCHIVE: Document completion and cross-reference
```

## 🔄 LEVEL TRANSITION HANDLING

```mermaid
graph TD
    L2["Level 2 Task"] --> Assess["Continuous<br>Assessment"]
    
    Assess --> Down["Downgrade to<br>Level 1"]
    Assess --> Up["Upgrade to<br>Level 3/4"]
    
    Down --> L1Trigger["Triggers:<br>- Simpler than expected<br>- Quick fix possible<br>- Single component"]
    
    Up --> L34Trigger["Triggers:<br>- More complex<br>- Multiple components<br>- Design needed"]
    
    L1Trigger --> L1Switch["Switch to<br>Level 1 Workflow"]
    L34Trigger --> L34Switch["Switch to<br>Level 3/4 Workflow"]
```

## 🔄 INTEGRATION WITH MEMORY BANK

```mermaid
graph TD
    Workflow["Level 2<br>Workflow"] --> PB["Update<br>projectbrief.md"]
    Workflow --> AC["Update<br>activeContext.md"]
    Workflow --> TM["Maintain<br>tasks.md"]
    Workflow --> PM["Update<br>progress.md"]
    
    PB & AC & TM & PM --> MB["Memory Bank<br>Integration"]
    MB --> NextTask["Transition to<br>Next Task"]
```

## 🚨 EFFICIENCY PRINCIPLE

Remember:

```
┌─────────────────────────────────────────────────────┐
│ Level 2 workflow balances PROCESS with EFFICIENCY.  │
│ Follow the structure but avoid unnecessary overhead. │
└─────────────────────────────────────────────────────┘
```

This ensures that simple enhancements are implemented with the right level of documentation and process. 