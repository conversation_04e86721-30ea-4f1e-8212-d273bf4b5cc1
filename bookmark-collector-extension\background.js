function flattenBookmarks(bookmarkTreeNodes) {
    const bookmarks = [];
    const stack = [...bookmarkTreeNodes];
    while (stack.length > 0) {
        const node = stack.pop();
        if (node.url) {
            bookmarks.push({
                title: node.title,
                url: node.url
            });
        }
        if (node.children) {
            stack.push(...node.children);
        }
    }
    return bookmarks;
}

function syncBookmarks() {
    return new Promise((resolve) => {
        chrome.bookmarks.getTree((bookmarkTreeNodes) => {
            const allBookmarks = flattenBookmarks(bookmarkTreeNodes);
            chrome.storage.local.set({ bookmarks: allBookmarks }, () => {
                console.log('书签已同步并存储。总数:', allBookmarks.length);
                resolve();
            });
        });
    });
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'sync-bookmarks') {
        syncBookmarks().then(() => {
            sendResponse({ status: 'success' });
        });
        return true; // Indicates that the response is sent asynchronously
    }
});

console.log('后台脚本已加载。'); 