document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('pacmanCanvas');
    const ctx = canvas.getContext('2d');
    const scoreEl = document.getElementById('score');
    const livesEl = document.getElementById('lives');

    // 游戏板参数 (经典Pac-Man是28x36个瓦片，每个瓦片8x8像素)
    const TILE_SIZE = 16;
    const BOARD_COLS = 28;
    const BOARD_ROWS = 36;

    canvas.width = BOARD_COLS * TILE_SIZE;
    canvas.height = BOARD_ROWS * TILE_SIZE;

    let score = 0;
    let lives = 3;

    // 迷宫布局 (0: path, 1: wall, 2: power pellet, 3: ghost door, 4: ghost house, 5: pacman start, 6: tunnel, 7: pellet)
    // This is a simplified representation. A full Pac-Man map is more complex.
    const map = [
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,2,7,7,7,7,7,7,7,7,7,7,7,1,1,7,7,7,7,7,7,7,7,7,7,7,2,1],
        [1,7,1,1,1,7,1,1,1,7,1,1,7,1,1,7,1,1,7,1,1,1,7,1,1,1,7,1],
        [1,7,1,1,1,7,1,1,1,7,1,1,7,1,1,7,1,1,7,1,1,1,7,1,1,1,7,1],
        [1,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,1],
        [1,7,1,1,1,7,1,7,1,1,1,1,1,1,1,1,1,1,1,7,1,7,1,1,1,7,7,1],
        [1,7,7,7,7,7,1,7,7,7,7,7,7,1,1,7,7,7,7,7,1,7,7,7,7,7,7,1],
        [1,1,1,1,1,7,1,1,1,7,1,1,0,1,1,0,1,1,7,1,1,1,7,1,1,1,1,1],
        [1,1,1,1,1,7,1,0,0,0,0,0,0,0,0,0,0,0,0,0,1,7,1,1,1,1,1,1],
        [1,1,1,1,1,7,1,0,1,1,1,1,3,3,3,3,1,1,1,0,1,7,1,1,1,1,1,1],
        [1,7,7,7,7,7,0,0,1,4,4,4,4,4,4,4,4,4,1,0,0,7,7,7,7,7,7,1],
        [6,7,1,1,1,7,1,0,1,4,4,4,4,4,4,4,4,4,1,0,1,7,1,1,1,0,0,6], // Tunnel row, ensure paths next to tunnel are clear or pellets
        [1,7,7,7,7,7,1,0,1,4,4,4,4,4,4,4,4,4,1,0,1,7,7,7,7,7,7,1],
        [1,1,1,1,1,7,1,0,1,1,1,1,1,1,1,1,1,1,1,0,1,7,1,1,1,1,1,1],
        [1,1,1,1,1,7,0,0,0,0,0,0,0,5,5,0,0,0,0,0,0,7,1,1,1,1,1,1], 
        [1,1,1,1,1,7,1,0,1,1,1,1,1,1,1,1,1,1,1,0,1,7,1,1,1,1,1,1],
        [1,7,7,7,7,7,1,0,0,0,0,0,0,1,1,0,0,0,0,0,1,7,7,7,7,7,7,1],
        [1,7,1,1,1,7,1,1,1,7,1,1,0,1,1,0,1,1,7,1,1,1,7,1,1,1,7,1],
        [1,7,7,7,1,7,7,7,7,7,7,7,7,0,0,7,7,7,7,7,7,7,1,7,7,7,7,1],
        [1,1,1,7,1,7,1,0,1,1,1,1,1,1,1,1,1,1,1,0,1,7,1,7,1,1,1,1],
        [1,7,7,7,0,7,1,0,0,0,0,0,0,1,1,0,0,0,0,0,1,7,0,7,7,7,7,1],
        [1,7,1,1,1,1,1,1,1,7,1,1,0,1,1,0,1,1,7,1,1,1,1,1,1,1,7,1],
        [1,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,1],
        [1,2,7,7,7,7,7,7,7,7,7,7,7,1,1,7,7,7,7,7,7,7,7,7,7,7,2,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        // Fill remaining rows to reach BOARD_ROWS (36 total)
        // These are typically not playable area in classic Pac-Man, but part of the screen for UI or empty space.
        // For simplicity, we'll make them solid walls for now, or extend the pattern.
        // Let's keep it simple and just make a bottom border.
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
        [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
    ];

    function isObstacle(col, row, entityType = 'pacman') {
        if (col < 0 || col >= BOARD_COLS || row < 0 || row >= BOARD_ROWS) {
            // Allow Pac-Man to move off-screen for tunnel if on tunnel row
            if (entityType === 'pacman' && row >= 11 && row < 12) return false;
            return true; // Out of bounds is an obstacle for most cases
        }
        const tile = map[row][col];

        if (tile === 1) return true; // Wall is always an obstacle
        
        // Tunnel is an obstacle if entityType is ghost
        if (tile === 6) return entityType === 'ghost'; 

        if (entityType === 'pacman') {
            if (tile === 3 || tile === 4) return true; // Ghost door and house for Pac-Man
        } else if (entityType === 'ghost') {
            // Ghosts logic for tiles 3 & 4:
            // Can pass through door (3) to exit/enter house.
            // Can move within house (4).
            // For now, we won't restrict ghosts from tile 3 & 4.
            // More specific logic for entering/exiting house will be tied to ghost states (e.g., EATEN state)
            if (tile === 3 || tile === 4) return false;
        }
        return false; // Path, power pellet, pellet etc. are not obstacles
    }

    function drawWall(x, y) {
        ctx.fillStyle = 'blue'; // Classic Pac-Man wall color
        ctx.fillRect(x * TILE_SIZE, y * TILE_SIZE, TILE_SIZE, TILE_SIZE);
    }

    function drawPellet(col, row) {
        ctx.beginPath();
        ctx.arc(col * TILE_SIZE + TILE_SIZE / 2, row * TILE_SIZE + TILE_SIZE / 2, TILE_SIZE / 8, 0, Math.PI * 2);
        ctx.fillStyle = 'yellow';
        ctx.fill();
        ctx.closePath();
    }

    function drawPowerPellet(col, row) {
        ctx.beginPath();
        ctx.arc(col * TILE_SIZE + TILE_SIZE / 2, row * TILE_SIZE + TILE_SIZE / 2, TILE_SIZE / 4, 0, Math.PI * 2);
        ctx.fillStyle = 'orange'; // Or make it blink
        ctx.fill();
        ctx.closePath();
    }

    function drawMap() {
        for (let row = 0; row < BOARD_ROWS; row++) {
            for (let col = 0; col < BOARD_COLS; col++) {
                if (map[row] && map[row][col] !== undefined) { // Check if row and cell exist
                    const tile = map[row][col];
                    if (tile === 1) { // Wall
                        drawWall(col, row);
                    } else if (tile === 7) { // Pellet
                        drawPellet(col, row);
                    } else if (tile === 2) { // Power Pellet
                        drawPowerPellet(col, row);
                    }
                }
            }
        }
    }

    // Pac-Man 对象
    const pacman = {
        x: 15 * TILE_SIZE, // Corrected starting position to be on a path tile (15, 23)
        y: 23 * TILE_SIZE,   // Corrected starting position
        radius: TILE_SIZE * 0.6,
        speed: TILE_SIZE / 14,
        dx: 0,
        dy: 0,
        color: '#FFFF00',
        mouthOpen: true, 
        mouthAngle: 0.2, 
        direction: 'right',
        desiredDirection: null, // Added to buffer input

        draw: function() {
            ctx.beginPath();
            let startAngle, endAngle;
            let mouthCenterX = this.x + TILE_SIZE / 2;
            let mouthCenterY = this.y + TILE_SIZE / 2;
            switch (this.direction) {
                case 'right':
                    startAngle = this.mouthAngle * Math.PI;
                    endAngle = (2 - this.mouthAngle) * Math.PI;
                    break;
                case 'left':
                    startAngle = (1 + this.mouthAngle) * Math.PI;
                    endAngle = (1 - this.mouthAngle) * Math.PI;
                    break;
                case 'up':
                    startAngle = (1.5 + this.mouthAngle) * Math.PI;
                    endAngle = (1.5 - this.mouthAngle) * Math.PI;
                    break;
                case 'down':
                    startAngle = (0.5 + this.mouthAngle) * Math.PI;
                    endAngle = (0.5 - this.mouthAngle) * Math.PI;
                    break;
            }

            if (this.mouthOpen) {
                ctx.arc(mouthCenterX, mouthCenterY, this.radius, startAngle, endAngle);
            } else {
                ctx.arc(mouthCenterX, mouthCenterY, this.radius, 0, 2 * Math.PI);
            }
            ctx.lineTo(mouthCenterX, mouthCenterY);
            ctx.fillStyle = this.color;
            ctx.fill();
            ctx.closePath();
        },

        update: function() {
            const collisionEpsilon = this.speed * 0.1; // Tolerance for floating point alignment, adjust if needed
            const isAlignedX = Math.abs(this.x % TILE_SIZE) < collisionEpsilon || Math.abs(this.x % TILE_SIZE) > TILE_SIZE - collisionEpsilon;
            const isAlignedY = Math.abs(this.y % TILE_SIZE) < collisionEpsilon || Math.abs(this.y % TILE_SIZE) > TILE_SIZE - collisionEpsilon;

            if (this.desiredDirection) {
                let targetDx = 0;
                let targetDy = 0;
                let canPotentiallyTurn = false;

                if (this.desiredDirection === 'left') { 
                    targetDx = -this.speed; 
                    if (isAlignedY) canPotentiallyTurn = true; 
                } else if (this.desiredDirection === 'right') { 
                    targetDx = this.speed; 
                    if (isAlignedY) canPotentiallyTurn = true; 
                } else if (this.desiredDirection === 'up') { 
                    targetDy = -this.speed; 
                    if (isAlignedX) canPotentiallyTurn = true; 
                } else if (this.desiredDirection === 'down') { 
                    targetDy = this.speed; 
                    if (isAlignedX) canPotentiallyTurn = true; 
                }

                if (canPotentiallyTurn) {
                    // Snap current position to grid before checking next tile
                    const currentSnappedX = Math.round(this.x / TILE_SIZE) * TILE_SIZE;
                    const currentSnappedY = Math.round(this.y / TILE_SIZE) * TILE_SIZE;
                    
                    let checkCol = Math.round(currentSnappedX / TILE_SIZE);
                    let checkRow = Math.round(currentSnappedY / TILE_SIZE);

                    if (targetDx > 0) checkCol++;       // Moving right, check tile to the right
                    else if (targetDx < 0) checkCol--;  // Moving left, check tile to the left
                    else if (targetDy > 0) checkRow++;  // Moving down, check tile below
                    else if (targetDy < 0) checkRow--;  // Moving up, check tile above
                    
                    if (!isObstacle(checkCol, checkRow, 'pacman')) {
                        this.dx = targetDx;
                        this.dy = targetDy;
                        this.direction = this.desiredDirection;
                        this.desiredDirection = null;
                        // Ensure Pac-Man is perfectly aligned on the grid axis he is now leaving
                        if (targetDx !== 0) { // if moving horizontally
                           this.y = currentSnappedY;
                        }
                        if (targetDy !== 0) { // if moving vertically
                           this.x = currentSnappedX;
                        }
                    }
                }
            }

            let potentialX = this.x + this.dx;
            let potentialY = this.y + this.dy;
            let inTunnelTransition = false;

            // Tunnel logic - check before collision
            const tunnelRowStartY = 11 * TILE_SIZE;
            const tunnelRowEndY = 12 * TILE_SIZE;

            if (this.dy === 0) { // Only allow tunnel transit if moving horizontally
                if (this.direction === 'left' && this.x <= 0 && this.y >= tunnelRowStartY && this.y < tunnelRowEndY) {
                    this.x = canvas.width - TILE_SIZE;
                    potentialX = this.x; 
                    inTunnelTransition = true;
                } else if (this.direction === 'right' && this.x >= canvas.width - TILE_SIZE && this.y >= tunnelRowStartY && this.y < tunnelRowEndY) {
                    this.x = 0;
                    potentialX = this.x; 
                    inTunnelTransition = true;
                }
            }


            if (!inTunnelTransition) {
                // Horizontal Collision Check
                if (this.dx > 0) { // Moving Right
                    let checkCol = Math.floor((this.x + this.dx + TILE_SIZE - collisionEpsilon) / TILE_SIZE); // Check slightly before full tile edge
                    let centerRow = Math.round((this.y) / TILE_SIZE); 
                    if (isObstacle(checkCol, centerRow, 'pacman') && (checkCol * TILE_SIZE) < (this.x + this.dx + TILE_SIZE - collisionEpsilon) ) {
                        potentialX = checkCol * TILE_SIZE - TILE_SIZE; 
                        this.dx = 0; 
                    }
                } else if (this.dx < 0) { // Moving Left
                    let checkCol = Math.floor((this.x + this.dx + collisionEpsilon) / TILE_SIZE);
                    let centerRow = Math.round((this.y) / TILE_SIZE);
                    if (isObstacle(checkCol, centerRow, 'pacman') && (checkCol + 1) * TILE_SIZE > (this.x + this.dx + collisionEpsilon) ) {
                        potentialX = (checkCol + 1) * TILE_SIZE; 
                        this.dx = 0; 
                    }
                }
                this.x = potentialX;

                // Vertical Collision Check 
                potentialY = this.y + this.dy; 

                if (this.dy > 0) { // Moving Down
                    let checkRow = Math.floor((this.y + this.dy + TILE_SIZE - collisionEpsilon) / TILE_SIZE);
                    let centerCol = Math.round((this.x) / TILE_SIZE); 
                    if (isObstacle(centerCol, checkRow, 'pacman') && (checkRow * TILE_SIZE) < (this.y + this.dy + TILE_SIZE - collisionEpsilon) ) {
                        potentialY = checkRow * TILE_SIZE - TILE_SIZE;
                        this.dy = 0; 
                    }
                } else if (this.dy < 0) { // Moving Up
                    let checkRow = Math.floor((this.y + this.dy + collisionEpsilon) / TILE_SIZE);
                    let centerCol = Math.round((this.x) / TILE_SIZE);
                    if (isObstacle(centerCol, checkRow, 'pacman') && (checkRow + 1) * TILE_SIZE > (this.y + this.dy + collisionEpsilon) ) {
                        potentialY = (checkRow + 1) * TILE_SIZE;
                        this.dy = 0; 
                    }
                }
                this.y = potentialY;
            } else {
                 this.x = potentialX; // Apply tunnel movement
            }

            // Check for pellet/power pellet consumption
            const pacmanCol = Math.floor((this.x + TILE_SIZE / 2) / TILE_SIZE);
            const pacmanRow = Math.floor((this.y + TILE_SIZE / 2) / TILE_SIZE);

            if (map[pacmanRow] && map[pacmanRow][pacmanCol] !== undefined) {
                if (map[pacmanRow][pacmanCol] === 7) { // Ate a pellet
                    map[pacmanRow][pacmanCol] = 0; // Remove pellet
                    score += 10;
                } else if (map[pacmanRow][pacmanCol] === 2) { // Ate a power pellet
                    map[pacmanRow][pacmanCol] = 0; // Remove power pellet
                    score += 50;
                    // TODO: Implement power pellet effect (ghosts become vulnerable)
                }
            }

            this.mouthOpen = !this.mouthOpen; 
        }
    };

    // 幽灵类
    class Ghost {
        constructor(x, y, color, initialDx = 0, initialDy = 0) {
            this.x = x * TILE_SIZE;
            this.y = y * TILE_SIZE;
            this.radius = TILE_SIZE * 0.6;
            this.speed = TILE_SIZE / 18;
            this.dx = initialDx * this.speed;
            this.dy = initialDy * this.speed;
            if(initialDx === 0 && initialDy === 0 && this.dx === 0 && this.dy === 0) { 
                this.dx = this.speed; 
            }
            this.originalColor = color;
            this.color = color;
            this.mode = 'NORMAL'; // NORMAL, FRIGHTENED, EATEN
            this.frightenedTimer = 0;
            this.frightenedColor = 'blue';
            this.eatenColor = '#666'; // Color when eaten, or just draw eyes
            this.ghostEatenValue = 200;
            this.targetTileX = null; // For returning to base when eaten
            this.targetTileY = null;
            // Define ghost house entry point (example, adjust as needed)
            this.ghostHouseEntry = { x: 13 * TILE_SIZE, y: 11 * TILE_SIZE }; 
        }

        draw() {
            ctx.beginPath();
            const bodyCenterX = this.x + TILE_SIZE / 2;
            const bodyCenterY = this.y + TILE_SIZE / 2;
            const bodyHeight = this.radius * 1.5;
            const eyeRadius = this.radius * 0.2;
            const eyeOffsetX = this.radius * 0.3;
            const eyeOffsetY = -this.radius * 0.2;

            let currentColor = this.color;
            if (this.mode === 'FRIGHTENED') {
                currentColor = this.frightenedColor;
                // Optional: make frightened ghosts blink
                if (this.frightenedTimer < 2000 && Math.floor(this.frightenedTimer / 250) % 2 === 0) {
                    currentColor = 'white'; // Blink white when timer is low
                }
            } else if (this.mode === 'EATEN') {
                // Draw only eyes when eaten, moving towards ghost house
                ctx.beginPath();
                ctx.arc(bodyCenterX - eyeOffsetX, bodyCenterY + eyeOffsetY, eyeRadius, 0, Math.PI * 2);
                ctx.arc(bodyCenterX + eyeOffsetX, bodyCenterY + eyeOffsetY, eyeRadius, 0, Math.PI * 2);
                ctx.fillStyle = 'white';
                ctx.fill();

                ctx.beginPath();
                let pupilOffsetX = 0;
                let pupilOffsetY = 0;
                // Make pupils point towards ghost house entry when eaten
                const angleToHouse = Math.atan2(this.ghostHouseEntry.y - (this.y + TILE_SIZE /2), this.ghostHouseEntry.x - (this.x + TILE_SIZE / 2));
                pupilOffsetX = Math.cos(angleToHouse) * eyeRadius * 0.4;
                pupilOffsetY = Math.sin(angleToHouse) * eyeRadius * 0.4;

                ctx.arc(bodyCenterX - eyeOffsetX + pupilOffsetX, bodyCenterY + eyeOffsetY + pupilOffsetY, eyeRadius * 0.5, 0, Math.PI*2);
                ctx.arc(bodyCenterX + eyeOffsetX + pupilOffsetX, bodyCenterY + eyeOffsetY + pupilOffsetY, eyeRadius * 0.5, 0, Math.PI*2);
                ctx.fillStyle = 'black';
                ctx.fill();
                return; // Skip drawing body if eaten
            }

            ctx.arc(bodyCenterX, bodyCenterY, this.radius, Math.PI, 0); 
            ctx.lineTo(bodyCenterX + this.radius, bodyCenterY + bodyHeight); 
            ctx.lineTo(bodyCenterX + this.radius * 0.6, bodyCenterY + bodyHeight * 0.8);
            ctx.lineTo(bodyCenterX, bodyCenterY + bodyHeight);
            ctx.lineTo(bodyCenterX - this.radius * 0.6, bodyCenterY + bodyHeight * 0.8);
            ctx.lineTo(bodyCenterX - this.radius, bodyCenterY + bodyHeight); 
            ctx.closePath();
            ctx.fillStyle = currentColor;
            ctx.fill();

            // Draw eyes (common for NORMAL and FRIGHTENED)
            ctx.beginPath();
            ctx.arc(bodyCenterX - eyeOffsetX, bodyCenterY + eyeOffsetY, eyeRadius, 0, Math.PI * 2);
            ctx.arc(bodyCenterX + eyeOffsetX, bodyCenterY + eyeOffsetY, eyeRadius, 0, Math.PI * 2);
            ctx.fillStyle = 'white';
            ctx.fill();

            ctx.beginPath();
            let pupilOffsetX = 0;
            let pupilOffsetY = 0;
            if (this.dx > 0) pupilOffsetX = eyeRadius * 0.4;
            else if (this.dx < 0) pupilOffsetX = -eyeRadius * 0.4;
            if (this.dy > 0) pupilOffsetY = eyeRadius * 0.4;
            else if (this.dy < 0) pupilOffsetY = -eyeRadius * 0.4;

            ctx.arc(bodyCenterX - eyeOffsetX + pupilOffsetX, bodyCenterY + eyeOffsetY + pupilOffsetY, eyeRadius * 0.5, 0, Math.PI*2);
            ctx.arc(bodyCenterX + eyeOffsetX + pupilOffsetX, bodyCenterY + eyeOffsetY + pupilOffsetY, eyeRadius * 0.5, 0, Math.PI*2);
            ctx.fillStyle = 'black';
            ctx.fill();
        }

        chooseNewDirection(isInitial = false) {
            const currentCol = Math.floor(this.x / TILE_SIZE); // Current tile, not center for this check
            const currentRow = Math.floor(this.y / TILE_SIZE);
            
            const possibleMoves = [
                { dx: 0, dy: -this.speed, dir: 'up' },
                { dx: 0, dy: this.speed, dir: 'down' },
                { dx: -this.speed, dy: 0, dir: 'left' },
                { dx: this.speed, dy: 0, dir: 'right' }
            ];
            
            let validMoves = [];

            for (const move of possibleMoves) {
                let nextTileCol = currentCol;
                let nextTileRow = currentRow;

                if (move.dir === 'up') nextTileRow--;
                else if (move.dir === 'down') nextTileRow++;
                else if (move.dir === 'left') nextTileCol--;
                else if (move.dir === 'right') nextTileCol++;

                if (!isObstacle(nextTileCol, nextTileRow, 'ghost')) {
                    if (isInitial || (move.dx !== -this.dx || move.dy !== -this.dy ) || possibleMoves.filter(pm => !isObstacle(currentCol + (pm.dx === 0 ? 0 : (pm.dx > 0 ? 1 : -1)), currentRow + (pm.dy === 0 ? 0 : (pm.dy > 0 ? 1 : -1)), 'ghost')).length <=1 ) { // Allow 180 turn if it's the only way out or initial
                        validMoves.push(move);
                    }
                }
            }
            
            if (!isInitial && validMoves.length === 0 && (this.dx !== 0 || this.dy !==0) ) { // If moving and no valid new paths (stuck in a niche that wasn't a 180)
                 // Try to find the 180 turn if it's valid (might have been excluded above)
                for (const move of possibleMoves) {
                    if (move.dx === -this.dx && move.dy === -this.dy) {
                         let nextTileCol = currentCol;
                         let nextTileRow = currentRow;
                         if (move.dir === 'up') nextTileRow--; else if (move.dir === 'down') nextTileRow++;
                         else if (move.dir === 'left') nextTileCol--; else if (move.dir === 'right') nextTileCol++;
                         
                         if (!isObstacle(nextTileCol, nextTileRow, 'ghost')) {
                            validMoves.push(move);
                            break;
                         }
                    }
                }
            }


            if (validMoves.length > 0) {
                const chosenMove = validMoves[Math.floor(Math.random() * validMoves.length)];
                this.dx = chosenMove.dx;
                this.dy = chosenMove.dy;
            } else {
                // If truly stuck (e.g. spawned in a 1x1 box or error), stop.
                this.dx = 0;
                this.dy = 0;
            }
        }

        update() {
            if (this.mode === 'FRIGHTENED') {
                this.frightenedTimer -= 1000/60; // Assuming 60 FPS
                if (this.frightenedTimer <= 0) {
                    this.mode = 'NORMAL';
                    this.color = this.originalColor;
                    // Optional: slightly increase speed after frightened mode or revert to normal speed
                }
            }

            if (this.mode === 'EATEN') {
                // Move towards ghost house entry. This is a simplified pathfinding.
                const targetXCenter = this.ghostHouseEntry.x + TILE_SIZE / 2;
                const targetYCenter = this.ghostHouseEntry.y + TILE_SIZE / 2;
                const currentXCenter = this.x + TILE_SIZE / 2;
                const currentYCenter = this.y + TILE_SIZE / 2;

                const diffX = targetXCenter - currentXCenter;
                const diffY = targetYCenter - currentYCenter;

                if (Math.abs(diffX) < this.speed && Math.abs(diffY) < this.speed) {
                    // Reached the ghost house (or close enough)
                    this.mode = 'NORMAL'; // Or a specific 'IN_HOUSE' mode
                    this.color = this.originalColor;
                    this.x = this.ghostHouseEntry.x; // Snap to position
                    this.y = this.ghostHouseEntry.y;
                    this.dx = this.speed; // Start moving out again or stay put
                    this.dy = 0;
                    return; // Skip normal movement logic for this frame
                }

                // Simplified movement: prioritize axis with larger difference
                // No collision check while returning, ghosts pass through walls when eaten
                const angle = Math.atan2(diffY, diffX);
                this.dx = Math.cos(angle) * this.speed * 2; // Move faster when eaten
                this.dy = Math.sin(angle) * this.speed * 2;
                
                this.x += this.dx;
                this.y += this.dy;
                return; // Skip normal movement logic
            }

            const currentSpeed = (this.mode === 'FRIGHTENED') ? this.speed * 0.75 : this.speed;
            const isAtTileCenter = (Math.abs(this.x % TILE_SIZE) < currentSpeed/2) && (Math.abs(this.y % TILE_SIZE) < currentSpeed/2);

            if (isAtTileCenter) {
                let lookAheadCol = Math.floor(this.x / TILE_SIZE);
                let lookAheadRow = Math.floor(this.y / TILE_SIZE);

                if (this.dx > 0) lookAheadCol++;
                else if (this.dx < 0) lookAheadCol--;
                else if (this.dy > 0) lookAheadRow++;
                else if (this.dy < 0) lookAheadRow--;

                // Ghosts should not try to enter the tunnel from the ends (tile 6)
                if (map[lookAheadRow] && map[lookAheadRow][lookAheadCol] === 6) {
                     // Find another direction if possible
                     this.chooseNewDirection();
                } else if (this.dx !== 0 || this.dy !== 0) { // If currently moving
                    if (isObstacle(lookAheadCol, lookAheadRow, 'ghost')) {
                        this.chooseNewDirection(); // Path ahead is blocked
                    } else {
                        // Optional: random turn at clear intersections (less likely if not at exact center)
                         if (isAtTileCenter && Math.random() < 0.15) { // 15% chance to turn
                             this.chooseNewDirection();
                         }
                    }
                } else { // Ghost is stopped, must choose a direction
                    this.chooseNewDirection(true); // isInitial = true to force a pick if possible
                }
            }

            let targetX = this.x + this.dx;
            let targetY = this.y + this.dy;

            // Adjust dx/dy based on currentSpeed if it changed
            if (this.dx !== 0) this.dx = Math.sign(this.dx) * currentSpeed;
            if (this.dy !== 0) this.dy = Math.sign(this.dy) * currentSpeed;

            targetX = this.x + this.dx;
            targetY = this.y + this.dy;

            // Horizontal Collision (similar to Pac-Man's logic)
            if (this.dx > 0) { // Moving Right
                let checkCol = Math.floor((this.x + this.dx + TILE_SIZE - 1) / TILE_SIZE);
                let r1 = Math.floor(this.y / TILE_SIZE);
                let r2 = Math.floor((this.y + TILE_SIZE - 1) / TILE_SIZE);
                if ((isObstacle(checkCol, r1, 'ghost') || isObstacle(checkCol, r2, 'ghost')) && (checkCol * TILE_SIZE) < (this.x + this.dx + TILE_SIZE - 1) ) { // Check slightly before edge
                    targetX = checkCol * TILE_SIZE - TILE_SIZE; // Align to wall
                     if (isAtTileCenter) this.chooseNewDirection(); else this.dx = 0; // Choose new direction only if centered
                }
            } else if (this.dx < 0) { // Moving Left
                let checkCol = Math.floor((this.x + this.dx) / TILE_SIZE);
                let r1 = Math.floor(this.y / TILE_SIZE);
                let r2 = Math.floor((this.y + TILE_SIZE - 1) / TILE_SIZE);
                 if ((isObstacle(checkCol, r1, 'ghost') || isObstacle(checkCol, r2, 'ghost')) && (checkCol + 1) * TILE_SIZE > (this.x + this.dx) ) { // Check slightly after edge
                    targetX = (checkCol + 1) * TILE_SIZE; // Align to wall
                     if (isAtTileCenter) this.chooseNewDirection(); else this.dx = 0; // Choose new direction only if centered
                }
            }
            this.x = targetX;

            // Vertical Collision (similar to Pac-Man's logic)
            if (this.dy > 0) { // Moving Down
                let checkRow = Math.floor((this.y + this.dy + TILE_SIZE - 1) / TILE_SIZE);
                let c1 = Math.floor(this.x / TILE_SIZE); // Use current this.x after potential horizontal alignment
                let c2 = Math.floor((this.x + TILE_SIZE - 1) / TILE_SIZE);
                 if ((isObstacle(c1, checkRow, 'ghost') || isObstacle(c2, checkRow, 'ghost')) && (checkRow * TILE_SIZE) < (this.y + this.dy + TILE_SIZE - 1) ) { // Check slightly before edge
                    targetY = checkRow * TILE_SIZE - TILE_SIZE; // Align to wall
                     if (isAtTileCenter) this.chooseNewDirection(); else this.dy = 0; // Choose new direction only if centered
                }
            } else if (this.dy < 0) { // Moving Up
                let checkRow = Math.floor(targetY / TILE_SIZE);
                let c1 = Math.floor(this.x / TILE_SIZE);
                let c2 = Math.floor((this.x + TILE_SIZE - 1) / TILE_SIZE);
                 if ((isObstacle(c1, checkRow, 'ghost') || isObstacle(c2, checkRow, 'ghost')) && (checkRow + 1) * TILE_SIZE > (this.y + this.dy) ) { // Check slightly after edge
                    targetY = (checkRow + 1) * TILE_SIZE; // Align to wall
                     if (isAtTileCenter) this.chooseNewDirection(); else this.dy = 0; // Choose new direction only if centered
                }
            }
            this.y = targetY;
            
            // Tunnel logic for ghosts (applied AFTER collision and position update)
            const tunnelRowY = 11 * TILE_SIZE; 
            // Ghost must be horizontally aligned with the tunnel row tile and moving horizontally
            if (this.dy === 0 && this.y >= tunnelRowY && this.y < 12 * TILE_SIZE) { 
                if (this.x <= -this.speed && this.dx < 0) { 
                    this.x = canvas.width - this.speed; // Adjust position by speed for smooth wrapping
                } else if (this.x >= canvas.width && this.dx > 0) { 
                    this.x = 0 + this.speed; // Adjust position by speed for smooth wrapping
                }
            }
        }
    }

    const ghosts = [];
    // Adjusted ghost starting positions to be inside their house or just outside the door
    ghosts.push(new Ghost(13, 10, 'red', 0, -1));    // Blinky (red) - starts just above ghost house door on a path
    ghosts.push(new Ghost(11, 11, 'pink', 1, 0));   // Pinky (pink) - starts in house
    ghosts.push(new Ghost(13, 11, 'cyan', -1, 0));  // Inky (cyan) - starts in house
    ghosts.push(new Ghost(15, 11, 'orange', 1, 0)); // Clyde (orange) - starts in house

    // 键盘事件监听
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'ArrowUp':
            case 'w':
                pacman.desiredDirection = 'up';
                break;
            case 'ArrowDown':
            case 's':
                pacman.desiredDirection = 'down';
                break;
            case 'ArrowLeft':
            case 'a':
                pacman.desiredDirection = 'left';
                break;
            case 'ArrowRight':
            case 'd':
                pacman.desiredDirection = 'right';
                break;
        }
    });

    function gameLoop() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        drawMap(); // Draw the map first

        pacman.update();
        pacman.draw();

        ghosts.forEach(ghost => {
            ghost.update();
            ghost.draw();
        });
        
        checkCollisions(); // Check for collisions after updates and before drawing next frame UI

        scoreEl.textContent = score;
        livesEl.textContent = lives;

        requestAnimationFrame(gameLoop);
    }

    function checkCollisions() {
        const pacmanCenterX = pacman.x + TILE_SIZE / 2;
        const pacmanCenterY = pacman.y + TILE_SIZE / 2;

        ghosts.forEach(ghost => {
            if (ghost.mode === 'EATEN') return; // Already eaten, no collision

            const ghostCenterX = ghost.x + TILE_SIZE / 2;
            const ghostCenterY = ghost.y + TILE_SIZE / 2;

            const dx = pacmanCenterX - ghostCenterX;
            const dy = pacmanCenterY - ghostCenterY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < pacman.radius + ghost.radius - (TILE_SIZE * 0.2) ) { // Adjusted collision threshold
                if (ghost.mode === 'FRIGHTENED') {
                    score += ghost.ghostEatenValue;
                    ghost.mode = 'EATEN';
                    // Ghost will now return to house via its update logic
                } else if (ghost.mode === 'NORMAL') {
                    // TODO: Pac-Man loses a life
                    lives--; 
                    console.log("Pac-Man hit by normal ghost! Lives:", lives);
                    if (lives <= 0) {
                        console.log("Game Over!");
                        // TODO: Implement proper game over (stop loop, show message)
                        alert("Game Over!"); // Placeholder
                        document.location.reload(); // Simple reset for now
                    } else {
                        // Reset positions (simple reset for now)
                        pacman.x = 15 * TILE_SIZE;
                        pacman.y = 23 * TILE_SIZE;
                        pacman.dx = 0;
                        pacman.dy = 0;
                        pacman.direction = 'right';
                        pacman.desiredDirection = null;
                        ghosts.forEach(g => {
                            // Basic reset for ghosts, ideally to their starting points and modes
                            // For now, just put them near the house and normal mode
                            g.x = g.originalStartingX || g.ghostHouseEntry.x; // Need to store original X
                            g.y = g.originalStartingY || g.ghostHouseEntry.y; // Need to store original Y
                            g.mode = 'NORMAL';
                            g.color = g.originalColor;
                            g.frightenedTimer = 0;
                        });
                    }
                }
            }
        });
    }
    
    function initGhostStartPositions() {
        ghosts[0].originalStartingX = 13 * TILE_SIZE;
        ghosts[0].originalStartingY = 10 * TILE_SIZE;
        ghosts[1].originalStartingX = 11 * TILE_SIZE;
        ghosts[1].originalStartingY = 11 * TILE_SIZE;
        ghosts[2].originalStartingX = 13 * TILE_SIZE;
        ghosts[2].originalStartingY = 11 * TILE_SIZE;
        ghosts[3].originalStartingX = 15 * TILE_SIZE;
        ghosts[3].originalStartingY = 11 * TILE_SIZE;
        ghosts.forEach(g => {
            g.x = g.originalStartingX;
            g.y = g.originalStartingY;
        });
    }

    // 开始游戏循环
    initGhostStartPositions(); // Initialize ghost starting positions correctly
    gameLoop();
}); 